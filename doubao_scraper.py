"""
豆包爬虫 - 用于自动化豆包聊天数据提取的主爬虫类

此模块提供主要的 DoubaoScraper 类，作为从豆包聊天网站
爬取对话数据的入口点。
"""

import asyncio
import json
from typing import Dict, Any, Optional, List
from pathlib import Path
import logging

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

try:
    from .core.config_manager import ConfigManager
    from .core.page_objects import DoubaoPage
    from .core.data_processor import DataProcessor
    from .core.human_behavior import HumanBehavior
    from .core.prompt_manager import PromptManager
    from .utils.logger import setup_logger
    from .utils.helpers import validate_parameters, retry_on_failure
except ImportError:
    # 处理直接运行时的导入问题
    from core.config_manager import ConfigManager
    from core.page_objects import DoubaoPage
    from core.data_processor import DataProcessor
    from core.human_behavior import HumanBehavior
    from core.prompt_manager import PromptManager
    from utils.logger import setup_logger
    from utils.helpers import validate_parameters, retry_on_failure


class DoubaoScraper:
    """
    用于从豆包聊天网站提取对话数据的主爬虫类。

    此类提供自动化提取对话数据的公共接口，
    包括思考过程和聊天结果。
    """

    def __init__(self, config_path: Optional[str] = None, proxy_config: Optional[Dict[str, str]] = None):
        """
        初始化豆包爬虫。

        参数:
            config_path: 配置文件路径（可选）
            proxy_config: 代理配置字典（可选）
        """
        # 加载配置
        self.config_manager = ConfigManager(config_path)

        # 设置日志
        logging_config = self.config_manager.get_logging_config()
        self.logger = setup_logger(logging_config)

        # 初始化组件
        self.data_processor = DataProcessor(self.config_manager.get_data_config())
        self.prompt_manager = PromptManager()

        # 浏览器相关属性
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.doubao_page: Optional[DoubaoPage] = None
        self.human_behavior: Optional[HumanBehavior] = None

        # 如果提供了代理配置则覆盖
        if proxy_config:
            self._update_proxy_config(proxy_config)

        self.logger.info("豆包爬虫初始化成功")

    async def scrape_conversation(self, language: str, topic: str, proposition_direction: str) -> Dict[str, Any]:
        """
        从豆包聊天中爬取对话数据的主要公共方法。

        这是爬虫的主要入口点。它接受三个必需参数，
        并返回包含过滤后对话内容的结构化JSON数据（仅包含思考过程和聊天结果）。

        参数:
            language: 对话语言（例如："中文", "English"）
            topic: 对话主题（例如："人工智能", "Climate Change"）
            proposition_direction: 命题方向（例如："支持", "反对", "中性"）

        返回:
            包含结构化对话数据的字典，JSON格式:
            {
                "metadata": {...},
                "thinking_processes": [...],
                "chat_results": [...],
                "conversation_flow": [...]
            }

        异常:
            ValueError: 如果参数无效
            Exception: 如果爬取失败
        """
        # 验证输入参数
        if not validate_parameters(language, topic, proposition_direction):
            raise ValueError("参数无效：language、topic 和 proposition_direction 必须是非空字符串")

        self.logger.info(f"开始对话爬取 - 语言: {language}, 主题: {topic}, 方向: {proposition_direction}")

        try:
            # 初始化浏览器
            await self._initialize_browser()

            # 导航到聊天页面
            await self.doubao_page.navigate_to_chat()
            await self.doubao_page.wait_for_page_ready()

            # 开始前模拟人类行为
            await self.human_behavior.random_delay()
            await self.human_behavior.random_mouse_movement()

            # 使用AI文章模板构建对话提示
            prompt = self._construct_prompt(language, topic, proposition_direction)

            # 设置对话并发送消息（包括浏览器级别重试）
            result = await self._send_message_with_browser_retry(prompt)
            if not result["success"]:
                raise Exception(f"发送消息或接收响应失败: {result.get('error')}")

            # 提取并处理捕获的API响应
            api_response_data = result.get("response_data")
            if api_response_data:
                # 处理捕获的流式响应
                structured_data = self._process_api_response(
                    api_response_data, language, topic, proposition_direction
                )
            else:
                # 回退：尝试从页面元素提取（传统方法）
                self.logger.warning("未捕获到API响应，回退到页面提取")
                raw_messages = await self.doubao_page.get_chat_messages()
                structured_data = self.data_processor.process_conversation_data(
                    raw_messages, language, topic, proposition_direction
                )

            self.logger.info("对话爬取成功完成")
            return structured_data

        except Exception as e:
            self.logger.error(f"爬取失败: {e}")
            return self._create_error_response(str(e))

        finally:
            await self._cleanup()

    def set_proxy(self, proxy_config: Dict[str, str]) -> None:
        """
        为爬虫设置代理配置。

        参数:
            proxy_config: 包含代理设置的字典:
                - server: 代理服务器URL（必需）
                - username: 代理用户名（可选）
                - password: 代理密码（可选）
        """
        self._update_proxy_config(proxy_config)
        self.logger.info("代理配置已更新")

    async def _initialize_browser(self) -> None:
        """初始化 Playwright 浏览器和页面。"""
        try:
            self.playwright = await async_playwright().start()

            # 获取浏览器配置
            browser_config = self.config_manager.get_browser_config()
            proxy_config = self.config_manager.get_proxy_config()

            # 启动浏览器
            launch_options = {
                "headless": browser_config.get("headless", False),
                "timeout": browser_config.get("timeout", 30000)
            }

            self.browser = await self.playwright.chromium.launch(**launch_options)

            # 如果配置了代理则创建带代理的上下文
            context_options = {
                "viewport": browser_config.get("viewport", {"width": 1920, "height": 1080}),
                "user_agent": browser_config.get("user_agent")
            }

            if proxy_config:
                context_options["proxy"] = {
                    "server": proxy_config["server"],
                    "username": proxy_config.get("username"),
                    "password": proxy_config.get("password")
                }

            self.context = await self.browser.new_context(**context_options)
            self.page = await self.context.new_page()

            # 初始化页面对象
            self.doubao_page = DoubaoPage(self.page)
            self.human_behavior = HumanBehavior(self.page, self.config_manager.get_human_behavior_config())

            self.logger.info("浏览器初始化成功")

        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {e}")
            await self._cleanup()
            raise

    def _construct_prompt(self, language: str, topic: str, proposition_direction: str) -> str:
        """
        基于参数使用AI文章模板构建对话提示。

        参数:
            language: 对话语言
            topic: 对话主题
            proposition_direction: 命题方向

        返回:
            从 ai_article.txt 模板构建的提示字符串
        """
        try:
            # 使用AI文章提示模板
            prompt = self.prompt_manager.get_ai_article_prompt(language, topic, proposition_direction)
            self.logger.info(f"使用 ai_article 模板构建提示")
            return prompt

        except Exception as e:
            self.logger.error(f"加载 ai_article 提示模板失败: {e}")
            # 回退到简单提示
            if language.lower() in ['中文', 'chinese', 'zh']:
                prompt = f"请用中文讨论关于'{topic}'的话题，我的立场是{proposition_direction}。请提供详细的分析和思考过程。"
            else:
                prompt = f"Please discuss the topic of '{topic}' in {language}. My position is {proposition_direction}. Please provide detailed analysis and thinking process."

            self.logger.warning("由于模板加载失败，使用回退提示")
            return prompt

    def _is_completion_successful(self, result: Dict[str, Any]) -> bool:
        """
        检测 completion 请求是否真正成功完成。

        Args:
            result: _send_message_and_wait 返回的结果

        Returns:
            True if completion is successful, False otherwise
        """
        try:
            # 基本成功检查
            if not result.get("success"):
                self.logger.warning(f"Basic success check failed: {result.get('error')}")
                return False

            # 检查是否有响应数据
            response_data = result.get("response_data")
            if not response_data:
                self.logger.warning("No response data received")
                return False

            # 检查是否有事件或处理过的内容
            events = response_data.get("events", [])
            js_processed = response_data.get("js_processed_content", {})

            # 如果有 JavaScript 处理的内容，检查其有效性
            if js_processed.get("has_js_processing"):
                thinking_content = js_processed.get("thinking_content", "")
                chat_content = js_processed.get("chat_content", "")

                if thinking_content.strip() or chat_content.strip():
                    self.logger.info("Found valid JavaScript-processed content")
                    return True
                else:
                    self.logger.warning("JavaScript processing enabled but no valid content found")

            # 检查事件数量
            if len(events) < 10:  # 至少应该有一些事件
                self.logger.warning(f"Too few events received: {len(events)}")
                return False

            # 检查是否有完整文本
            full_text = response_data.get("full_text", "")
            if len(full_text.strip()) < 100:  # 至少应该有一些实质内容
                self.logger.warning(f"Response text too short: {len(full_text)} chars")
                return False

            # 检查是否标记为完成
            if not response_data.get("response_complete", False):
                self.logger.warning("Response not marked as complete")
                # 不直接返回 False，因为有时候内容是完整的但标记可能有问题

            self.logger.info(f"Completion validation passed: {len(events)} events, {len(full_text)} chars")
            return True

        except Exception as e:
            self.logger.error(f"Error validating completion success: {e}")
            return False

    async def _send_message_with_browser_retry(self, message: str, max_browser_retries: int = 3) -> Dict[str, Any]:
        """
        发送消息并等待响应，支持浏览器级别的重试。

        如果 completion 请求没有完全成功，会关闭浏览器实例并重新初始化后重试。

        Args:
            message: 要发送的消息
            max_browser_retries: 最大浏览器重试次数

        Returns:
            包含成功状态和响应数据的字典
        """
        last_error = None

        for browser_retry in range(max_browser_retries):
            try:
                self.logger.info(f"Browser retry attempt {browser_retry + 1}/{max_browser_retries}")

                # 如果不是第一次尝试，需要重新初始化浏览器
                if browser_retry > 0:
                    self.logger.info("Reinitializing browser for retry...")
                    await self._cleanup()
                    await asyncio.sleep(2)  # 短暂等待确保清理完成

                    # 重新初始化浏览器
                    await self._initialize_browser()

                    # 重新导航到聊天页面
                    await self.doubao_page.navigate_to_chat()
                    await self.doubao_page.wait_for_page_ready()

                    # 重新模拟人类行为
                    await self.human_behavior.random_delay()
                    await self.human_behavior.random_mouse_movement()

                # 尝试发送消息（这里仍然使用原有的重试装饰器）
                result = await self._send_message_and_wait(message)

                # 检查 completion 是否真正成功
                if self._is_completion_successful(result):
                    self.logger.info(f"Message sent and completion successful on browser retry {browser_retry + 1}")
                    return result
                else:
                    last_error = f"Completion validation failed on browser retry {browser_retry + 1}"
                    self.logger.warning(last_error)

                    # 如果不是最后一次重试，继续下一次
                    if browser_retry < max_browser_retries - 1:
                        continue
                    else:
                        # 最后一次重试也失败了，返回失败结果
                        return {
                            "success": False,
                            "error": f"All browser retries failed. Last error: {last_error}",
                            "response_data": result.get("response_data")  # 仍然返回最后的数据，可能部分有用
                        }

            except Exception as e:
                last_error = f"Browser retry {browser_retry + 1} failed with exception: {str(e)}"
                self.logger.error(last_error)

                # 如果不是最后一次重试，继续下一次
                if browser_retry < max_browser_retries - 1:
                    continue
                else:
                    # 最后一次重试也失败了
                    return {"success": False, "error": last_error, "response_data": None}

        # 理论上不应该到达这里
        return {"success": False, "error": "Unexpected end of browser retry loop", "response_data": None}

    @retry_on_failure(max_retries=3, delay=2.0)
    async def _send_message_and_wait(self, message: str) -> Dict[str, Any]:
        """
        设置对话并发送消息，包含重试逻辑，捕获API响应。

        此方法实现完整的工作流程：
        1. 点击深度思考选择按钮
        2. 选择深度思考项目1
        3. 输入消息并按回车
        4. 等待并捕获流式API响应

        参数:
            message: 要发送的消息

        返回:
            包含成功状态和捕获的响应数据的字典
        """
        try:
            # 开始前添加类人延迟
            await self.human_behavior.random_delay(1000, 2000)

            # 执行完整的设置和发送工作流程，并捕获响应
            result = await self.doubao_page.setup_conversation_and_send_message(message, wait_for_completion=True)

            if result["success"]:
                # 如果获得响应则模拟阅读时间
                if result.get("response_data"):
                    response_text = result["response_data"].get("full_text", "")
                    await self.human_behavior.simulate_reading_delay(len(response_text))

                self.logger.info("消息发送成功并捕获响应")
            else:
                self.logger.error(f"发送消息失败: {result.get('error')}")

            return result

        except Exception as e:
            self.logger.error(f"设置对话和发送消息失败: {e}")
            return {"success": False, "error": str(e), "response_data": None}

    def _process_api_response(self, api_response_data: Dict[str, Any], language: str,
                            topic: str, proposition_direction: str) -> Dict[str, Any]:
        """
        将捕获的API响应数据处理为结构化格式。

        参数:
            api_response_data: 捕获的API响应数据
            language: 语言参数
            topic: 主题参数
            proposition_direction: 命题方向参数

        返回:
            结构化的对话数据
        """
        try:
            from datetime import datetime

            # 从API响应中提取内容
            full_text = api_response_data.get("full_text", "")
            thinking_content = api_response_data.get("thinking_content", [])
            final_answer = api_response_data.get("final_answer", "")
            events = api_response_data.get("events", [])
            js_processed_content = api_response_data.get("js_processed_content", {})

            # 如果事件数组为空，则从full_text解析事件
            if not events and full_text:
                events = self._parse_events_from_full_text(full_text)

            self.logger.info(f"处理API响应: {len(events)} 个事件, JS处理: {js_processed_content.get('has_js_processing', False)}")

            # 创建结构化数据格式
            structured_data = {
                "metadata": {
                    "language": language,
                    "topic": topic,
                    "proposition_direction": proposition_direction,
                    "timestamp": datetime.now().isoformat(),
                    "source": "api_capture",
                    "event_count": len(events),
                    "response_complete": api_response_data.get("response_complete", False),
                    "full_text_length": len(full_text),
                    "processing_method": "javascript_processing" if js_processed_content.get("has_js_processing") else "python_processing"
                },
                # "thinking_processes": self._extract_thinking_from_api_response_with_js(thinking_content, events, js_processed_content),
                # "chat_results": self._extract_chat_results_from_api_response_with_js(final_answer, full_text, js_processed_content),
                # "conversation_flow": self._build_flow_from_api_response(events),
                "raw_api_data": {
                    "thinking_content": js_processed_content.get('thinking_content'),
                    "chat_content": js_processed_content.get('chat_content'),
                    "response_complete": api_response_data.get("response_complete", False),
                    # "full_text": full_text,
                    # "event_count": len(events),
                    # "js_processed_content": js_processed_content
                }
            }

            self.logger.info(f"已处理API响应: {len(events)} 个事件, {len(full_text)} 个字符")
            return structured_data

        except Exception as e:
            self.logger.error(f"处理API响应失败: {e}")
            return self._create_error_response(f"API响应处理失败: {e}")

    def _extract_thinking_from_api_response(self, thinking_content: List[str], events: List[Dict]) -> List[Dict[str, Any]]:
        """从API响应中提取思考过程。"""
        thinking_processes = []

        try:
            self.logger.info(f"开始思考提取: {len(thinking_content)} 个显式内容, {len(events)} 个事件")

            # 处理显式思考内容
            for i, content in enumerate(thinking_content):
                if content and content.strip():
                    thinking_processes.append({
                        "content": self._clean_json_string(content),
                        "sequence": i + 1,
                        "source": "thinking_extraction",
                        "length": len(content)
                    })
                    self.logger.info(f"添加显式思考内容 {i+1}")

            # 从事件中提取思考 - 收集片段并组合它们
            thinking_fragments = []
            for i, event in enumerate(events):
                if isinstance(event, dict):
                    thinking_text = self._extract_thinking_from_event(event)
                    if thinking_text:
                        thinking_fragments.append(thinking_text)
                        self.logger.info(f"发现思考片段 {i}: {thinking_text[:30]}...")
                    elif i < 10:  # 调试前10个事件
                        self.logger.debug(f"事件 {i} 没有可用数据")

            # 将思考片段组合成连贯内容
            if thinking_fragments:
                combined_thinking = self._combine_thinking_fragments(thinking_fragments)
                if combined_thinking:
                    thinking_processes.append({
                        "content": combined_thinking,
                        "sequence": len(thinking_processes) + 1,
                        "source": "combined_fragments",
                        "fragment_count": len(thinking_fragments),
                        "length": len(combined_thinking)
                    })
                    self.logger.info(f"将 {len(thinking_fragments)} 个片段组合成思考过程: {combined_thinking[:100]}...")

            self.logger.info(f"总共提取的思考过程: {len(thinking_processes)}")

        except Exception as e:
            self.logger.error(f"从API响应提取思考时出错: {e}")

        return thinking_processes

    def _extract_chat_results_from_api_response(self, final_answer: str, full_text: str) -> List[Dict[str, Any]]:
        """从API响应中提取聊天结果。"""
        chat_results = []

        try:
            # 清理并格式化最终答案
            if final_answer and final_answer.strip():
                cleaned_answer = self._clean_json_string(final_answer)
                chat_results.append({
                    "content": cleaned_answer,
                    "type": "final_answer",
                    "length": len(cleaned_answer),
                    "source": "api_extraction"
                })

            # 只有当完整文本与最终答案不同且包含有意义内容时才添加
            if full_text and full_text.strip() and full_text != final_answer:
                # 从完整文本中移除思考内容以避免重复
                cleaned_full_text = self._remove_thinking_from_text(full_text)
                if cleaned_full_text and len(cleaned_full_text.strip()) > 100:  # 只有实质内容才添加
                    chat_results.append({
                        "content": self._clean_json_string(cleaned_full_text),
                        "type": "full_response",
                        "length": len(cleaned_full_text),
                        "source": "api_capture"
                    })

        except Exception as e:
            self.logger.error(f"从API响应提取聊天结果时出错: {e}")

        return chat_results

    def _extract_thinking_from_api_response_with_js(self, thinking_content: List[str],
                                                   events: List[Dict],
                                                   js_processed_content: Dict[str, Any]) -> List[Dict[str, Any]]:
        """使用JavaScript处理支持提取思考过程。"""
        thinking_processes = []

        try:
            # 优先使用JavaScript端处理的结果
            if js_processed_content.get("has_js_processing") and js_processed_content.get("thinking_content"):
                thinking_text = js_processed_content.get("thinking_content", "")
                if thinking_text.strip():
                    thinking_processes.append({
                        "content": thinking_text,
                        "sequence": 1,
                        "source": "javascript_processing",
                        "fragment_count": len(js_processed_content.get("thinking_fragments", [])),
                        "length": len(thinking_text),
                        "confidence": 0.9,  # JS处理的高置信度
                        "keywords": ["思考", "分析"] if any(k in thinking_text for k in ["思考", "分析"]) else []
                    })
                    self.logger.info(f"使用JavaScript处理的思考内容: {thinking_text[:100]}...")
            else:
                # 回退到原有的Python处理
                thinking_processes = self._extract_thinking_from_api_response(thinking_content, events)

        except Exception as e:
            self.logger.error(f"JS增强思考提取出错: {e}")
            # 回退到原有方法
            thinking_processes = self._extract_thinking_from_api_response(thinking_content, events)

        return thinking_processes

    def _extract_chat_results_from_api_response_with_js(self, final_answer: str,
                                                       full_text: str,
                                                       js_processed_content: Dict[str, Any]) -> List[Dict[str, Any]]:
        """使用JavaScript处理支持提取聊天结果。"""
        chat_results = []

        try:
            # 优先使用JavaScript端处理的结果
            if js_processed_content.get("has_js_processing") and js_processed_content.get("chat_content"):
                chat_text = js_processed_content.get("chat_content", "")
                if chat_text.strip():
                    chat_results.append({
                        "content": chat_text,
                        "sequence": 1,
                        "source": "javascript_processing",
                        "fragment_count": len(js_processed_content.get("chat_fragments", [])),
                        "length": len(chat_text),
                        "type": "final_answer",
                        "keywords": self._extract_keywords(chat_text)
                    })
                    self.logger.info(f"使用JavaScript处理的聊天内容: {chat_text[:100]}...")
            else:
                # 回退到原有的Python处理
                chat_results = self._extract_chat_results_from_api_response(final_answer, full_text)

        except Exception as e:
            self.logger.error(f"JS增强聊天提取出错: {e}")
            # 回退到原有方法
            chat_results = self._extract_chat_results_from_api_response(final_answer, full_text)

        return chat_results

    def _build_flow_from_api_response(self, events: List[Dict]) -> List[Dict[str, Any]]:
        """从API响应事件构建对话流程。"""
        flow = []

        try:
            for i, event in enumerate(events):
                if isinstance(event, dict):
                    text = str(event.get('text', '') or event.get('content', ''))
                    flow.append({
                        "sequence": i + 1,
                        "event_type": event.get('event_type', 'unknown'),
                        "content_preview": text[:100] + '...' if len(text) > 100 else text,
                        "content_length": len(text),
                        "event_id": event.get('id', f'event_{i}')
                    })

        except Exception as e:
            self.logger.error(f"从API响应构建流程时出错: {e}")

        return flow

    def _update_proxy_config(self, proxy_config: Dict[str, str]) -> None:
        """
        更新代理配置。

        参数:
            proxy_config: 代理配置字典
        """
        if "server" not in proxy_config:
            raise ValueError("代理配置必须包含 'server'")

        self.config_manager.update_config("proxy.enabled", True)
        self.config_manager.update_config("proxy.server", proxy_config["server"])

        if "username" in proxy_config:
            self.config_manager.update_config("proxy.username", proxy_config["username"])

        if "password" in proxy_config:
            self.config_manager.update_config("proxy.password", proxy_config["password"])

    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """
        创建标准化错误响应。

        参数:
            error_message: 错误消息

        返回:
            错误响应字典
        """
        return {
            "error": True,
            "message": error_message,
            "timestamp": None,
            "metadata": None,
            "thinking_processes": [],
            "chat_results": [],
            "conversation_flow": []
        }

    async def _cleanup(self) -> None:
        """清理浏览器资源。"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()

            self.logger.info("浏览器清理完成")

        except Exception as e:
            self.logger.error(f"清理过程中出错: {e}")

    async def __aenter__(self):
        """异步上下文管理器入口。"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出。"""
        await self._cleanup()

    def _clean_json_string(self, json_str: str) -> str:
        """通过移除多余的转义字符来清理JSON字符串。"""
        try:
            if not json_str:
                return ""

            cleaned = str(json_str)

            # 移除多层转义 - 但要更加小心
            iteration_count = 0
            max_iterations = 5  # 防止无限循环

            while '\\\\' in cleaned and iteration_count < max_iterations:
                cleaned = cleaned.replace('\\\\', '\\')
                iteration_count += 1

            # 处理转义引号
            cleaned = cleaned.replace('\\"', '"')

            # 处理其他常见转义序列 - 转换为空格以保持JSON兼容性
            cleaned = cleaned.replace('\\n', ' ')
            cleaned = cleaned.replace('\\r', ' ')
            cleaned = cleaned.replace('\\t', ' ')

            # 移除多余空白但保持结构
            import re
            cleaned = re.sub(r'\s+', ' ', cleaned).strip()

            return cleaned
        except Exception as e:
            self.logger.error(f"清理JSON字符串时出错: {e}")
            return str(json_str)

    def _extract_thinking_from_event(self, event: Dict) -> str:
        """从单个事件中提取思考内容。"""
        try:
            import json

            # 检查事件是否有event_data
            if 'event_data' in event:
                event_data_str = event['event_data']
                try:
                    # 解析前清理JSON字符串
                    cleaned_event_data = self._clean_json_string(event_data_str)
                    event_data = json.loads(cleaned_event_data)

                    # 查找消息内容
                    if 'message' in event_data and isinstance(event_data['message'], dict):
                        message = event_data['message']
                        if 'content' in message:
                            content_str = message['content']
                            try:
                                # 清理并解析内容JSON
                                cleaned_content = self._clean_json_string(content_str)
                                content_data = json.loads(cleaned_content)

                                # 提取think字段 - 即使只是片段
                                if 'think' in content_data and content_data['think']:
                                    thinking_text = self._clean_json_string(str(content_data['think']))
                                    # 返回即使是小片段 - 它们稍后会被组合
                                    if thinking_text.strip():
                                        self.logger.debug(f"发现思考片段: {thinking_text[:30]}...")
                                        return thinking_text.strip()

                            except json.JSONDecodeError:
                                # 内容可能不是JSON，检查思考关键词
                                cleaned_content = self._clean_json_string(content_str)
                                if any(keyword in cleaned_content.lower() for keyword in ['思考', 'thinking', '分析']):
                                    self.logger.debug(f"发现思考关键词: {cleaned_content[:30]}...")
                                    return cleaned_content.strip()

                except json.JSONDecodeError as e:
                    self.logger.debug(f"解析event_data JSON失败: {e}")
                    # 尝试从原始字符串提取思考内容
                    if any(keyword in event_data_str.lower() for keyword in ['思考', 'thinking', '分析', 'think']):
                        cleaned_raw = self._clean_json_string(event_data_str)
                        return cleaned_raw.strip()

            return ""

        except Exception as e:
            self.logger.error(f"从事件提取思考时出错: {e}")
            return ""

    def _combine_thinking_fragments(self, fragments: List[str]) -> str:
        """将思考片段组合成连贯内容。"""
        try:
            if not fragments:
                return ""

            # 过滤掉很短的片段（可能不完整）
            meaningful_fragments = [f for f in fragments if len(f.strip()) > 2]

            if not meaningful_fragments:
                return ""

            # 用适当的间距连接片段
            combined = ""
            for fragment in meaningful_fragments:
                fragment = fragment.strip()
                if fragment:
                    if combined and not combined.endswith(('。', '！', '？', '.', '!', '?')):
                        # 添加适当的连接符
                        if fragment[0].islower() or fragment.startswith(('，', '、', '的', '了', '着')):
                            combined += fragment
                        else:
                            combined += " " + fragment
                    else:
                        combined += fragment if not combined else " " + fragment

            # 清理组合后的文本
            combined = self._clean_json_string(combined)
            combined = ' '.join(combined.split())  # 规范化空白

            return combined.strip()

        except Exception as e:
            self.logger.error(f"组合思考片段时出错: {e}")
            return ' '.join(fragments) if fragments else ""

    def _remove_thinking_from_text(self, text: str) -> str:
        """从文本中移除思考内容以避免重复。"""
        try:
            import json

            # 如果文本包含JSON结构，尝试只提取非思考部分
            if text.strip().startswith('{'):
                try:
                    data = json.loads(text)
                    # 移除think字段并返回剩余内容
                    if isinstance(data, dict):
                        filtered_data = {k: v for k, v in data.items() if k not in ['think', 'thinking', 'cbatchat']}
                        if filtered_data:
                            return json.dumps(filtered_data, ensure_ascii=False, indent=2)
                except json.JSONDecodeError:
                    pass

            # 对于常规文本，移除看起来像思考内容的行
            lines = text.split('\n')
            filtered_lines = []

            for line in lines:
                line_lower = line.lower()
                # 跳过明显是思考内容的行
                if not any(keyword in line_lower for keyword in ['思考', 'thinking', '分析', 'analysis', '考虑']):
                    filtered_lines.append(line)

            return '\n'.join(filtered_lines).strip()

        except Exception as e:
            self.logger.error(f"从文本移除思考内容时出错: {e}")
            return text

    def _parse_events_from_full_text(self, full_text: str) -> List[Dict]:
        """从full_text字符串解析事件。"""
        events = []
        try:
            import json
            import re

            # 按事件边界分割full_text
            # 每个事件的格式似乎是: {"event_data":"...", "event_id":"...", "event_type":...}
            event_pattern = r'\{\"event_data\":\".*?\",\"event_id\":\".*?\",\"event_type\":\d+\}'

            matches = re.findall(event_pattern, full_text)

            for i, match in enumerate(matches):
                try:
                    event = json.loads(match)
                    events.append(event)
                except json.JSONDecodeError:
                    self.logger.warning(f"解析事件 {i} 失败: {match[:100]}...")
                    continue

            self.logger.info(f"从full_text解析了 {len(events)} 个事件")
            return events

        except Exception as e:
            self.logger.error(f"从full_text解析事件时出错: {e}")
            return []


# 直接使用的便利函数
async def scrape_doubao_conversation(language: str, topic: str, proposition_direction: str,
                                   config_path: Optional[str] = None,
                                   proxy_config: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """
    爬取豆包对话数据的便利函数。

    参数:
        language: 对话语言
        topic: 对话主题
        proposition_direction: 命题方向
        config_path: 配置文件路径（可选）
        proxy_config: 代理配置（可选）

    返回:
        结构化的对话数据
    """
    async with DoubaoScraper(config_path, proxy_config) as scraper:
        return await scraper.scrape_conversation(language, topic, proposition_direction)