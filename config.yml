# 豆包爬虫配置

# 浏览器设置
browser:
  headless: false
  timeout: 300000
  viewport:
    width: 1440
    height: 720
  user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"

# 代理设置（可选）
proxy:
  enabled: true
  server: "e369.kdltps.com:15818"  # 例如: "http://proxy.example.com:8080"
  username: "t15316677787531"
  password: "4sjsw6br"

# Metaso 登录设置
metaso_login:
  enabled: false  # 是否启用自动登录
  username: "18532167113"    # Metaso 账号用户名
  password: "123456Ll."    # Metaso 账号密码

# Metaso API 设置
metaso_api:
  token: "mk-33FE9BC71C0DAF91C7C3F9E61B5C3BD7"  # Metaso API 令牌
  timeout: 120  # API请求超时时间（秒）

# 人类行为模拟
human_behavior:
  typing_speed:
    min_delay: 50  # 按键间隔毫秒数
    max_delay: 150
  mouse_movement:
    enabled: true
    speed: 1.0  # 移动速度倍数
  scroll_behavior:
    enabled: true
    speed: 500  # 每次滚动像素数
    delay: 1000  # 滚动间隔毫秒数
  random_delays:
    min: 1000  # 最小延迟毫秒数
    max: 3000  # 最大延迟毫秒数

# 爬取设置
scraping:
  max_retries: 3
  retry_delay: 2000  # 毫秒
  wait_for_response: 50000  # 毫秒

# 日志记录
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: ""  # 留空则不保存日志文件，仅输出到控制台
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 数据处理
data:
  filter_content: true
  include_thinking: true
  include_chat_results: true
  exclude_ui_elements: true
