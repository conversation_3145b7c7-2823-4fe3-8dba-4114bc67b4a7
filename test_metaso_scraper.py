"""
Metaso.cn 爬虫测试脚本

此脚本用于测试 MetasoScraper 的基本功能。
"""

import asyncio
import json
import sys
from pathlib import Path

# 添加当前目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent))

from metaso_scraper import scrape_metaso_content


async def test_basic_functionality():
    """测试基本功能"""
    print("测试 Metaso 爬虫基本功能...")

    # 测试查询
    test_query = "记叙文：在生活中运用数学知识，最新内容"

    try:
        print(f"搜索查询: {test_query}")
        result = await scrape_metaso_content(test_query)

        print(f"搜索结果:")
        print(f"  成功: {result['success']}")
        print(f"  查询: {result['search_query']}")
        print(f"  会话ID: {result['session_id']}")

        if result['success']:
            print(f"  数据类型: {type(result['data'])}")
            if result['data']:
                if isinstance(result['data'], list):
                    print(f"  数据长度: {len(result['data'])}")
                    if len(result['data']) > 0:
                        print(f"  第一项预览: {str(result['data'][0])[:100]}...")
                else:
                    print(f"  数据预览: {str(result['data'])[:200]}...")
            else:
                print("  数据为空")
        else:
            print(f"  错误: {result.get('error', '未知错误')}")

        # 保存测试结果
        output_file = "test_metaso_result.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"  结果已保存到: {output_file}")

        return result['success']

    except Exception as e:
        print(f"测试失败: {e}")
        return False


async def test_error_handling():
    """测试错误处理"""
    print("\n测试错误处理...")

    # 测试空查询
    try:
        result = await scrape_metaso_content("")
        print("空查询测试失败 - 应该抛出异常")
        return False
    except ValueError:
        print("✓ 空查询正确抛出 ValueError")
    except Exception as e:
        print(f"✗ 空查询抛出了意外异常: {e}")
        return False

    # 测试空白查询
    try:
        result = await scrape_metaso_content("   ")
        print("空白查询测试失败 - 应该抛出异常")
        return False
    except ValueError:
        print("✓ 空白查询正确抛出 ValueError")
    except Exception as e:
        print(f"✗ 空白查询抛出了意外异常: {e}")
        return False

    return True


async def test_with_login():
    """测试登录功能"""
    print("\n测试登录功能...")

    # 登录配置 - 请替换为真实的用户名和密码
    login_config = {
        "username": "your_username_here",  # 请替换为真实用户名
        "password": "your_password_here"   # 请替换为真实密码
    }

    # 检查是否提供了真实的登录信息
    if login_config["username"] == "your_username_here":
        print("⚠️  警告: 请在代码中设置真实的用户名和密码")
        print("   在第 92-95 行修改 login_config")
        print("   跳过登录测试...")
        return True  # 跳过测试，不算失败

    # 测试查询
    test_query = "人工智能在教育中的应用"

    try:
        print(f"使用登录账号搜索: {test_query}")
        result = await scrape_metaso_content(test_query, login_config=login_config)

        print(f"登录搜索结果:")
        print(f"  成功: {result['success']}")
        print(f"  查询: {result['search_query']}")
        print(f"  会话ID: {result['session_id']}")

        if result['success']:
            print(f"  数据类型: {type(result['data'])}")
            if result['data']:
                if isinstance(result['data'], list):
                    print(f"  数据长度: {len(result['data'])}")
                    if len(result['data']) > 0:
                        print(f"  第一项预览: {str(result['data'][0])[:100]}...")
                else:
                    print(f"  数据预览: {str(result['data'])[:200]}...")
            else:
                print("  数据为空")

            # 保存登录测试结果
            output_file = "test_metaso_login_result.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"  登录测试结果已保存到: {output_file}")

        else:
            print(f"  错误: {result.get('error', '未知错误')}")

        return result['success']

    except Exception as e:
        print(f"登录测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("Metaso.cn 爬虫测试")
    print("=" * 40)

    # 运行基本功能测试
    basic_test_passed = await test_basic_functionality()

    # 运行错误处理测试
    error_test_passed = await test_error_handling()

    # 运行登录功能测试
    login_test_passed = await test_with_login()

    print("\n" + "=" * 40)
    print("测试结果:")
    print(f"  基本功能测试: {'通过' if basic_test_passed else '失败'}")
    print(f"  错误处理测试: {'通过' if error_test_passed else '失败'}")
    print(f"  登录功能测试: {'通过' if login_test_passed else '失败'}")

    if basic_test_passed and error_test_passed and login_test_passed:
        print("✓ 所有测试通过")
        return 0
    else:
        print("✗ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
