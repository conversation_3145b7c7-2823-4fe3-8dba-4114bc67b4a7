"""
Metaso API 客户端 - 使用官方API接口获取内容

此模块提供通过Metaso官方API接口获取搜索结果的功能，
作为爬虫方式的替代方案。
"""

import http.client
import json
import logging
import ssl
from pathlib import Path

try:
    from .core.config_manager import ConfigManager
    from .utils.logger import setup_logger
except ImportError:
    # 处理直接运行时的导入问题
    from core.config_manager import ConfigManager
    from utils.logger import setup_logger


class MetasoAPIClient:
    """
    Metaso API 客户端类，用于通过官方API获取搜索结果。
    """

    def __init__(self, config_path=None, api_token=None, verify_ssl=True):
        """
        初始化 Metaso API 客户端。

        参数:
            config_path: 配置文件路径（可选）
            api_token: API令牌（可选，优先级高于配置文件）
            verify_ssl: 是否验证SSL证书（默认True）
        """
        # 加载配置
        self.config_manager = ConfigManager(config_path)
        
        # 设置日志
        logging_config = self.config_manager.get_logging_config()
        self.logger = setup_logger(logging_config)
        
        # API配置
        self.api_token = api_token or self._get_api_token_from_config()
        self.base_host = "metaso.cn"
        self.api_endpoint = "/api/v1/ask"
        self.verify_ssl = verify_ssl
        
        if not self.api_token:
            raise ValueError("API令牌未配置，请在配置文件中设置或通过参数传入")
        
        self.logger.info("Metaso API 客户端初始化成功")

    def _get_api_token_from_config(self):
        """从配置文件获取API令牌。"""
        return self.config_manager.get('metaso_api.token')

    def search(self, query, scope="webpage", model="fast_thinking"):
        """
        使用API搜索内容。

        参数:
            query: 搜索查询字符串
            scope: 搜索范围，默认为"webpage"

        返回:
            包含搜索结果的字典:
            {
                "success": bool,
                "data": {...},  # API返回的原始数据
                "metadata": {...},
                "search_query": str
            }
        """
        if not query or not query.strip():
            raise ValueError("搜索查询不能为空")

        self.logger.info(f"开始API搜索 - 查询: {query}, 范围: {scope}")

        try:
            # 创建SSL上下文
            if self.verify_ssl:
                context = ssl.create_default_context()
            else:
                context = ssl.create_default_context()
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                self.logger.warning("SSL证书验证已禁用")
            
            # 建立HTTPS连接
            timeout = self.config_manager.get('metaso_api.timeout', 120)  # 默认120秒
            conn = http.client.HTTPSConnection(self.base_host, timeout=timeout, context=context)
            
            # 准备请求数据
            payload = json.dumps({
                "q": query.strip(),
                "scope": scope
            })
            
            # 设置请求头
            headers = {
                'Authorization': f'Bearer {self.api_token}',
                'Content-Type': 'application/json',
                'Accept': 'application/json'
            }
            
            # 发送请求
            self.logger.info(f"发送API请求到: {self.base_host}{self.api_endpoint}")
            conn.request("POST", self.api_endpoint, payload, headers)
            
            # 获取响应
            response = conn.getresponse()
            response_data = response.read()
            
            # 关闭连接
            conn.close()
            
            # 解析响应
            if response.status == 200:
                try:
                    parsed_data = json.loads(response_data.decode("utf-8"))
                    
                    # 构建成功响应
                    result = {
                        "success": True,
                        "data": parsed_data,
                        "metadata": {
                            "search_query": query,
                            "scope": scope,
                            "api_endpoint": self.api_endpoint,
                            "response_status": response.status,
                            "source": "metaso_api"
                        },
                        "search_query": query
                    }
                    
                    self.logger.info(f"API搜索成功完成")
                    return result
                    
                except json.JSONDecodeError as e:
                    error_msg = f"解析API响应JSON失败: {e}"
                    self.logger.error(error_msg)
                    return self._create_error_response(error_msg, query)
            else:
                error_msg = f"API请求失败，状态码: {response.status}, 响应: {response_data.decode('utf-8')}"
                self.logger.error(error_msg)
                return self._create_error_response(error_msg, query)
                
        except Exception as e:
            error_msg = f"API请求异常: {e}"
            self.logger.error(error_msg)
            return self._create_error_response(error_msg, query)

    def _create_error_response(self, error_message, search_query=""):
        """
        创建标准化错误响应。

        参数:
            error_message: 错误消息
            search_query: 搜索查询（可选）

        返回:
            错误响应字典
        """
        return {
            "success": False,
            "error": error_message,
            "data": None,
            "metadata": {
                "search_query": search_query,
                "api_endpoint": self.api_endpoint,
                "source": "metaso_api_error"
            },
            "search_query": search_query
        }


# 便利函数
def search_metaso_api(query, scope="webpage", config_path=None, api_token=None, verify_ssl=False):
    """
    使用Metaso API搜索内容的便利函数。

    参数:
        query: 搜索查询字符串
        scope: 搜索范围，默认为"webpage"
        config_path: 配置文件路径（可选）
        api_token: API令牌（可选）
        verify_ssl: 是否验证SSL证书（默认False以避免证书问题）

    返回:
        搜索结果字典
    """
    client = MetasoAPIClient(config_path, api_token, verify_ssl)
    return client.search(query, scope)


if __name__ == "__main__":
    # 测试代码
    import asyncio
    
    async def test_api():
        try:
            # 测试API搜索
            result = search_metaso_api("谁是这个世界上最美丽的女人")
            
            print(f"搜索结果:")
            print(f"  成功: {result['success']}")
            print(f"  查询: {result['search_query']}")
            
            if result['success']:
                print(f"  数据类型: {type(result['data'])}")
                print(f"  数据预览: {str(result['data'])[:200]}...")
                
                # 保存结果
                with open("test_api_result.json", 'w', encoding='utf-8') as f:
                    json.dump(result, f, ensure_ascii=False, indent=2)
                print("  结果已保存到: test_api_result.json")
            else:
                print(f"  错误: {result.get('error')}")
                
        except Exception as e:
            print(f"测试失败: {e}")
        finally:
            # 删除测试文件
            import os
            if os.path.exists("test_api_result.json"):
                # os.remove("test_api_result.json")
                print("  测试文件已删除")
    
    asyncio.run(test_api())
