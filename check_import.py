#!/usr/bin/env python3
"""
检查导入是否正常
"""

try:
    import metaso_scraper
    print("✅ metaso_scraper 导入成功")
except Exception as e:
    print(f"❌ metaso_scraper 导入失败: {e}")
    import traceback
    traceback.print_exc()

try:
    from core.page_objects import MetasoPage
    print("✅ MetasoPage 导入成功")
except Exception as e:
    print(f"❌ MetasoPage 导入失败: {e}")
    import traceback
    traceback.print_exc()
