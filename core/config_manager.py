"""
豆包爬虫的配置管理器

处理从YAML文件加载和管理配置。
"""

import os
import yaml
from typing import Dict, Any, Optional
from pathlib import Path


class ConfigManager:
    """管理豆包爬虫的配置设置。"""

    def __init__(self, config_path: Optional[str] = None):
        """
        初始化配置管理器。

        参数:
            config_path: 配置文件路径。如果为None，使用默认路径。
        """
        if config_path is None:
            # 默认使用与此脚本相同目录下的config.yml
            self.config_path = Path(__file__).parent.parent / "config.yml"
        else:
            self.config_path = Path(config_path)

        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """从YAML文件加载配置。"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
                return config or {}
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件未找到: {self.config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"解析YAML配置时出错: {e}")

    def get(self, key: str, default: Any = None) -> Any:
        """
        使用点号表示法获取配置值。

        参数:
            key: 配置键（例如：'browser.timeout'）
            default: 如果键未找到时的默认值

        返回:
            配置值或默认值
        """
        keys = key.split('.')
        value = self.config

        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default

        return value

    def get_browser_config(self) -> Dict[str, Any]:
        """获取浏览器配置。"""
        return self.get('browser', {})

    def get_proxy_config(self) -> Optional[Dict[str, Any]]:
        """如果启用则获取代理配置。"""
        proxy_config = self.get('proxy', {})
        if proxy_config.get('enabled', False):
            return proxy_config
        return None

    def get_metaso_login_config(self) -> Optional[Dict[str, str]]:
        """如果启用则获取 Metaso 登录配置。"""
        login_config = self.get('metaso_login', {})
        if login_config.get('enabled', False):
            username = login_config.get('username', '').strip()
            password = login_config.get('password', '').strip()
            if username and password:
                return {
                    'username': username,
                    'password': password
                }
        return None

    def get_human_behavior_config(self) -> Dict[str, Any]:
        """获取人类行为模拟配置。"""
        return self.get('human_behavior', {})

    def get_scraping_config(self) -> Dict[str, Any]:
        """获取爬取配置。"""
        return self.get('scraping', {})

    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置。"""
        return self.get('logging', {})

    def get_data_config(self) -> Dict[str, Any]:
        """获取数据处理配置。"""
        return self.get('data', {})

    def update_config(self, key: str, value: Any) -> None:
        """
        更新配置值。

        参数:
            key: 配置键（例如：'browser.timeout'）
            value: 新值
        """
        keys = key.split('.')
        config = self.config

        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]

        config[keys[-1]] = value

    def save_config(self) -> None:
        """将当前配置保存到文件。"""
        with open(self.config_path, 'w', encoding='utf-8') as file:
            yaml.dump(self.config, file, default_flow_style=False, allow_unicode=True)
