/**
 * EventSource 监控脚本
 * 用于捕获豆包聊天网站的 SSE 流数据
 */

// 全局存储 SSE 数据
window.sseData = {
    events: [],
    isComplete: false,
    error: null,
    currentStream: null,
    debugInfo: [],
    rawChunks: [],  // 存储原始数据块
    thinkingFragments: [],  // 存储思考内容片段
    chatFragments: [],      // 存储聊天内容片段
    processedContent: {     // 处理后的内容
        thinking: "",
        chat: "",
        combined: ""
    }
};

// 调试日志函数
window.debugLog = function (message, data) {
    console.log('SSE Debug:', message, data);
    window.sseData.debugInfo.push({
        timestamp: Date.now(),
        message: message,
        data: data
    });
};

// 清除 SSE 数据
window.clearSSEData = function () {
    window.sseData = {
        events: [],
        isComplete: false,
        error: null,
        currentStream: null,
        debugInfo: [],
        rawChunks: [],
        thinkingFragments: [],
        chatFragments: [],
        processedContent: {
            thinking: "",
            chat: "",
            combined: ""
        }
    };
    window.debugLog('SSE data cleared');
};


/**
 * 安全的 JSON 解析函数
 * @param {string} jsonString - 要解析的 JSON 字符串
 * @returns {object|null} 解析后的对象，解析失败返回 null
 */
function safeJsonParse(jsonString) {
    try {
        return JSON.parse(jsonString);
    } catch (error) {
        console.warn('JSON 解析失败:', error.message);
        return null;
    }
}

/**
 * 处理 meta_infos 数组中的嵌套 JSON 字符串
 * @param {Array} metaInfos - meta_infos 数组
 * @returns {Array} 处理后的数组
 */
function processMetaInfos(metaInfos) {
    if (!Array.isArray(metaInfos)) {
        return metaInfos;
    }

    return metaInfos.map(metaInfo => {
        if (metaInfo && typeof metaInfo.info === 'string') {
            const parsedInfo = safeJsonParse(metaInfo.info);
            if (parsedInfo !== null) {
                return {
                    ...metaInfo,
                    info: parsedInfo
                };
            }
        }
        return metaInfo;
    });
}

/**
 * 处理 SSE 数据的主函数
 * @param {string} sseDataString - 从 SSE 捕获到的原始字符串数据
 * @returns {object|null} 处理后的数据对象，处理失败返回 null
 */
function processSseData(sseDataString) {
    // 第一次解析：解析最外层的 JSON
    const dataLayer1 = safeJsonParse(sseDataString);
    if (!dataLayer1) {
        console.error('第一层 JSON 解析失败');
        return null;
    }

    // 检查是否存在 message.content
    if (!dataLayer1.event_data || typeof dataLayer1.event_data !== 'string') {
        console.warn('message.content 不存在或不是字符串，跳过内容解析');
        return dataLayer1;
    }

    // 第二次解析：解析 message.content
    const contentString = dataLayer1.event_data;
    const dataLayer2 = safeJsonParse(contentString);
    if (!dataLayer2) {
        console.warn('message.content JSON 解析失败，保持原始字符串');
        return dataLayer1;
    }

    // 第三次解析：处理 dataLayer2 中的 meta_infos
    if (dataLayer2.meta_infos) {
        dataLayer2.meta_infos = processMetaInfos(dataLayer2.meta_infos);
    }

    // 将解析后的对象重新组装回去
    dataLayer1.message.content = dataLayer2;

    // 处理外层的 meta_infos（如果存在）
    if (dataLayer1.message.meta_infos) {
        dataLayer1.message.meta_infos = processMetaInfos(dataLayer1.message.meta_infos);
    }

    return dataLayer1;
}

/**
 * 格式化输出处理后的数据
 * @param {object} processedData - 处理后的数据对象
 * @param {number} indent - 缩进空格数，默认为 2
 * @returns {string} 格式化后的 JSON 字符串
 */
function formatProcessedData(processedData, indent = 2) {
    return JSON.stringify(processedData, null, indent);
}


/**
 * 深度解析一个可能包含内嵌JSON字符串的值。
 * @param {*} value - 需要被解析的值.
 * @returns {*} 解析后的值.
 */
function deepParseJson(value) {
    // 1. 如果输入值是字符串类型
    if (typeof value === 'string') {
        // 2. 尝试将其作为JSON解析
        try {
            const parsed = JSON.parse(value);
            // 3. 如果解析成功，对解析出的结果递归调用本函数
            //    以处理更深层次的内嵌JSON
            return deepParseJson(parsed);
        } catch (e) {
            // 4. 如果解析失败，说明它只是一个普通字符串，直接返回
            return value;
        }
    }
    // 5. 如果输入值是数组，遍历其每个元素并递归调用
    else if (Array.isArray(value)) {
        return value.map(item => deepParseJson(item));
    }
    // 6. 如果输入值是对象，遍历其每个属性值并递归调用
    else if (typeof value === 'object' && value !== null) {
        // 使用 Object.keys 和 reduce 来创建一个新的、值被深度解析过的对象
        return Object.keys(value).reduce((acc, key) => {
            acc[key] = deepParseJson(value[key]);
            return acc;
        }, {});
    }
    // 7. 如果是数字、布尔值、null等其他类型，直接返回
    else {
        return value;
    }
}


/**
 * 完整的 SSE 数据处理流程
 * @param {string} sseDataString - 从 SSE 捕获到的原始字符串数据
 * @param {boolean} logResult - 是否打印结果到控制台，默认为 true
 * @returns {object|null} 处理后的数据对象
 */
function handleSseData(sseDataString, logResult = true) {
    console.log('开始处理 SSE 数据...');

    const processedData = deepParseJson(sseDataString);

    return processedData;
}


// 清理 JSON 字符串中的多余转义字符
function cleanJsonString(jsonStr) {
    try {
        // 移除多余的转义斜杠
        let cleaned = jsonStr;

        // 处理多层转义的情况
        while (cleaned.includes('\\\\')) {
            cleaned = cleaned.replace(/\\\\/g, '\\');
        }

        // 处理转义的引号
        cleaned = cleaned.replace(/\\"/g, '"');

        return cleaned;
    } catch (e) {
        window.debugLog('Error cleaning JSON string', e);
        return jsonStr;
    }
}

// 解析 SSE 数据块
function parseSSEChunk(chunk) {
    return handleSseData(chunk.split('data: ')[1]);
}

// 检查数据中是否包含思考内容
function checkForThinkingContent(data) {
    if (!data || typeof data !== 'object') return false;

    // 递归检查对象中的所有字段
    function searchForThinking(obj) {
        if (!obj || typeof obj !== 'object') return false;

        for (const [key, value] of Object.entries(obj)) {
            // 检查字段名是否包含思考相关关键词
            const keyLower = key.toLowerCase();
            if (keyLower.includes('think') || keyLower.includes('reasoning') ||
                keyLower.includes('analysis') || keyLower.includes('thought')) {
                return true;
            }

            // 检查字符串值是否包含思考相关内容
            if (typeof value === 'string') {
                const valueLower = value.toLowerCase();
                if (valueLower.includes('思考') || valueLower.includes('分析') ||
                    valueLower.includes('reasoning') || valueLower.includes('thinking')) {
                    return true;
                }
            }

            // 递归检查嵌套对象
            if (typeof value === 'object' && value !== null) {
                if (searchForThinking(value)) {
                    return true;
                }
            }
        }

        return false;
    }

    return searchForThinking(data);
}


// 合并思考片段
function combineThinkingFragments(fragments) {
    if (!fragments || fragments.length === 0) return '';

    // 过滤掉太短的片段
    const meaningfulFragments = fragments.filter(f => f.length > 2);

    if (meaningfulFragments.length === 0) return '';

    // 智能合并片段
    let combined = '';
    for (const fragment of meaningfulFragments) {
        if (combined) {
            // 检查是否需要添加空格
            const lastChar = combined.slice(-1);
            const firstChar = fragment.charAt(0);

            if (lastChar.match(/[。！？.!?]/) || firstChar.match(/[，、的了着]/)) {
                combined += fragment;
            } else {
                combined += ' ' + fragment;
            }
        } else {
            combined = fragment;
        }
    }

    return combined.trim();
}

// 处理流式响应数据
function processStreamData(chunk) {
    // 存储原始数据块
    window.sseData.rawChunks.push(chunk);

    // 解析 SSE 事件
    let event;
    try {
        const { event_data } = parseSSEChunk(chunk);
        event = event_data;
    } catch (e) {
        return;
    }
    if (!event) {
        return;
    }

    // 添加到事件列表并实时提取内容
    event.timestamp = Date.now();
    window.sseData.events.push(event);


    if (event.message?.content_type === 2008) {
        window.sseData.processedContent.thinking += event.message?.content?.think || '';
        window.sseData.processedContent.chat += event.message?.content?.text || '';
    }


    // 更新合并内容
    window.sseData.processedContent.combined =
        (window.sseData.processedContent.thinking + ' ' + window.sseData.processedContent.chat).trim();

    if (event.message?.content?.text) {
        console.log(event)
    }
}

// 监控 XMLHttpRequest
const originalXHROpen = XMLHttpRequest.prototype.open;
XMLHttpRequest.prototype.open = function (method, url, ...args) {
    this._url = url;
    return originalXHROpen.call(this, method, url, ...args);
};

// 监控 fetch 请求
const originalFetch = window.fetch;
window.fetch = function (url, options = {}) {
    const headers = options.headers || {};
    window.debugLog('Fetch detected', { url, options });

    // 检查是否是流式请求候选
    const isStreamingCandidate = (
        url.includes('completion')
    );

    if (isStreamingCandidate) {
        window.debugLog('Potential streaming request detected', { url, headers });
        window.sseData.currentStream = url;

        return originalFetch.call(this, url, options).then(response => {
            window.debugLog('Streaming response received', {
                url: response.url,
                status: response.status,
                contentType: response.headers.get('content-type')
            });

            // 检查是否是流式响应
            const contentType = response.headers.get('content-type') || '';
            const isStreamResponse = (
                contentType.includes('text/event-stream') ||
                contentType.includes('text/plain') ||
                contentType.includes('application/json')
            );

            if (isStreamResponse && response.body) {
                window.debugLog('Processing streaming response', contentType);

                // 克隆响应以读取流
                const responseClone = response.clone();
                const reader = responseClone.body.getReader();
                const decoder = new TextDecoder();

                function readStream() {
                    return reader.read().then(({ done, value }) => {
                        if (done) {
                            window.debugLog('Stream reading completed');
                            window.sseData.isComplete = true;
                            return;
                        }

                        const chunk = decoder.decode(value, { stream: true });
                        processStreamData(chunk);

                        return readStream();
                    });
                }

                readStream().catch(error => {
                    window.debugLog('Stream reading error', error);
                    window.sseData.error = error.toString();
                });
            }

            return response;
        }).catch(error => {
            window.debugLog('Fetch error', error);
            window.sseData.error = error.toString();
            throw error;
        });
    }

    return originalFetch.call(this, url, options);
};

// 监控 EventSource 创建
const originalEventSource = window.EventSource;
window.EventSource = function (url, eventSourceInitDict) {
    window.debugLog('EventSource created for', url);
    window.sseData.currentStream = url;
    window.sseData.isComplete = false;
    window.sseData.events = [];
    window.sseData.error = null;

    const eventSource = new originalEventSource(url, eventSourceInitDict);

    // 重写事件监听器
    const originalAddEventListener = eventSource.addEventListener;
    eventSource.addEventListener = function (type, listener, options) {
        const wrappedListener = function (event) {
            window.debugLog('EventSource ' + type + ' event', event.data);

            if (type === 'message' || type === 'data') {
                try {
                    const data = JSON.parse(event.data);
                    window.sseData.events.push({
                        type: type,
                        data: data,
                        timestamp: Date.now()
                    });
                } catch (e) {
                    window.sseData.events.push({
                        type: type,
                        data: event.data,
                        timestamp: Date.now()
                    });
                }
            }

            if (type === 'error') {
                window.sseData.error = 'EventSource error';
                window.sseData.isComplete = true;
            }

            return listener.call(this, event);
        };

        return originalAddEventListener.call(this, type, wrappedListener, options);
    };

    return eventSource;
};

// 监控脚本注入完成
window.debugLog('EventSource monitoring script injected successfully');

// 导出函数供 Python 调用
window.getSSEData = function () {
    return window.sseData;
};

window.getSSEEvents = function () {
    return window.sseData.events;
};

window.isSSEComplete = function () {
    return window.sseData.isComplete;
};

window.getSSEError = function () {
    return window.sseData.error;
};

window.getDebugInfo = function () {
    return window.sseData.debugInfo;
};

window.getRawChunks = function () {
    return window.sseData.rawChunks;
};

// 新增：获取处理后的内容
window.getProcessedContent = function () {
    return window.sseData.processedContent;
};

window.getThinkingFragments = function () {
    return window.sseData.thinkingFragments;
};

window.getChatFragments = function () {
    return window.sseData.chatFragments;
};

window.getThinkingContent = function () {
    return window.sseData.processedContent.thinking;
};

window.getChatContent = function () {
    return window.sseData.processedContent.chat;
};

window.getCombinedContent = function () {
    return window.sseData.processedContent.combined;
};
