"""
人类行为模拟模块

模拟类人交互以避免被反机器人系统检测。
"""

import asyncio
import random
import math
from typing import Tuple, Optional, Dict, Any
from playwright.async_api import Page, Locator
import logging


class HumanBehavior:
    """为网页自动化模拟类人行为。"""

    def __init__(self, page: Page, config: Dict[str, Any]):
        """
        初始化人类行为模拟器。

        参数:
            page: Playwright页面实例
            config: 人类行为配置
        """
        self.page = page
        self.config = config
        self.logger = logging.getLogger(__name__)
    
    async def random_delay(self, min_delay: Optional[int] = None, max_delay: Optional[int] = None) -> None:
        """
        添加随机延迟以模拟人类思考时间。

        参数:
            min_delay: 最小延迟（毫秒）
            max_delay: 最大延迟（毫秒）
        """
        if min_delay is None:
            min_delay = self.config.get('random_delays', {}).get('min', 1000)
        if max_delay is None:
            max_delay = self.config.get('random_delays', {}).get('max', 3000)

        delay = random.randint(min_delay, max_delay) / 1000.0
        await asyncio.sleep(delay)
        self.logger.debug(f"随机延迟: {delay:.2f}秒")

    async def human_type(self, element: Locator, text: str, clear_first: bool = True) -> None:
        """
        以类人速度和节奏输入文本。

        参数:
            element: 要输入的元素
            text: 要输入的文本
            clear_first: 是否先清除现有文本
        """
        try:
            if clear_first:
                await element.clear()

            typing_config = self.config.get('typing_speed', {})
            min_delay = typing_config.get('min_delay', 50)
            max_delay = typing_config.get('max_delay', 150)

            for char in text:
                await element.type(char)

                # 在按键之间添加随机延迟
                delay = random.randint(min_delay, max_delay) / 1000.0
                await asyncio.sleep(delay)

                # 偶尔添加更长的停顿（模拟思考）
                if random.random() < 0.1:  # 10%的概率
                    await asyncio.sleep(random.uniform(0.2, 0.8))

            self.logger.debug(f"以类人行为输入文本: {text[:30]}...")

        except Exception as e:
            self.logger.error(f"类人输入失败: {e}")
            # 回退到正常输入
            if clear_first:
                await element.clear()
            await element.fill(text)
    
    async def human_click(self, element: Locator, delay_before: bool = True) -> None:
        """
        以类人行为点击元素。

        参数:
            element: 要点击的元素
            delay_before: 是否在点击前添加延迟
        """
        try:
            if delay_before:
                await self.random_delay(200, 800)

            # 获取元素边界框以确定真实的点击位置
            box = await element.bounding_box()
            if box:
                # 在元素内的随机位置点击
                x = box['x'] + random.uniform(0.2, 0.8) * box['width']
                y = box['y'] + random.uniform(0.2, 0.8) * box['height']

                # 先移动鼠标到位置
                await self.move_mouse_to(x, y)
                await asyncio.sleep(random.uniform(0.1, 0.3))

                # 点击
                await self.page.mouse.click(x, y)
            else:
                # 回退到正常点击
                await element.click()

            self.logger.debug("执行了类人点击")

        except Exception as e:
            self.logger.error(f"类人点击失败: {e}")
            # 回退到正常点击
            await element.click()

    async def move_mouse_to(self, x: float, y: float) -> None:
        """
        以类人移动方式将鼠标移动到坐标。

        参数:
            x: 目标x坐标
            y: 目标y坐标
        """
        if not self.config.get('mouse_movement', {}).get('enabled', True):
            return

        try:
            # 获取当前鼠标位置（近似）
            current_x, current_y = await self._get_current_mouse_position()

            # 计算移动路径
            steps = max(10, int(math.sqrt((x - current_x)**2 + (y - current_y)**2) / 20))
            speed = self.config.get('mouse_movement', {}).get('speed', 1.0)

            for i in range(steps):
                progress = (i + 1) / steps

                # 使用缓动函数实现更自然的移动
                eased_progress = self._ease_in_out_cubic(progress)

                current_x_step = current_x + (x - current_x) * eased_progress
                current_y_step = current_y + (y - current_y) * eased_progress

                # 添加小的随机变化
                current_x_step += random.uniform(-2, 2)
                current_y_step += random.uniform(-2, 2)

                await self.page.mouse.move(current_x_step, current_y_step)
                await asyncio.sleep(0.01 / speed)

            # 最终移动到精确位置
            await self.page.mouse.move(x, y)

        except Exception as e:
            self.logger.error(f"自然鼠标移动失败: {e}")
            # 回退到直接移动
            await self.page.mouse.move(x, y)
    
    async def _get_current_mouse_position(self) -> Tuple[float, float]:
        """获取当前鼠标位置的近似值。"""
        # 由于无法获取实际鼠标位置，使用视口中心作为默认值
        viewport = self.page.viewport_size
        if viewport:
            return viewport['width'] / 2, viewport['height'] / 2
        return 960, 540  # 默认中心位置

    def _ease_in_out_cubic(self, t: float) -> float:
        """用于自然移动的三次缓动函数。"""
        if t < 0.5:
            return 4 * t * t * t
        else:
            return 1 - pow(-2 * t + 2, 3) / 2

    async def scroll_page(self, direction: str = "down", amount: Optional[int] = None) -> None:
        """
        以类人行为滚动页面。

        参数:
            direction: "up" 或 "down"
            amount: 滚动像素数（可选）
        """
        if not self.config.get('scroll_behavior', {}).get('enabled', True):
            return

        try:
            scroll_config = self.config.get('scroll_behavior', {})
            if amount is None:
                amount = scroll_config.get('speed', 500)

            delay = scroll_config.get('delay', 1000) / 1000.0

            # 确定滚动方向
            delta_y = amount if direction == "down" else -amount

            # 以较小增量执行滚动以实现更自然的行为
            increments = max(3, amount // 100)
            increment_size = delta_y / increments

            for _ in range(increments):
                await self.page.mouse.wheel(0, increment_size)
                await asyncio.sleep(0.1)

            # 滚动后添加延迟
            await asyncio.sleep(delay)

            self.logger.debug(f"向{direction}滚动了{amount}像素")

        except Exception as e:
            self.logger.error(f"自然滚动失败: {e}")
    
    async def simulate_reading_delay(self, text_length: int) -> None:
        """
        模拟阅读文本所需的时间。

        参数:
            text_length: 要"阅读"的文本长度
        """
        # 假设平均阅读速度为每分钟200个单词
        # 平均单词长度约为5个字符
        words = text_length / 5
        reading_time = (words / 200) * 60  # 转换为秒

        # 添加一些随机性和最小/最大边界
        reading_time = max(1, min(10, reading_time * random.uniform(0.8, 1.2)))

        await asyncio.sleep(reading_time)
        self.logger.debug(f"模拟阅读延迟: {reading_time:.2f}秒，文本长度{text_length}字符")

    async def random_mouse_movement(self) -> None:
        """执行随机鼠标移动以显得更像人类。"""
        if not self.config.get('mouse_movement', {}).get('enabled', True):
            return

        try:
            viewport = self.page.viewport_size
            if not viewport:
                return

            # 移动到视口内的随机位置
            x = random.uniform(100, viewport['width'] - 100)
            y = random.uniform(100, viewport['height'] - 100)

            await self.move_mouse_to(x, y)
            await asyncio.sleep(random.uniform(0.5, 1.5))

        except Exception as e:
            self.logger.error(f"执行随机鼠标移动失败: {e}")
