"""
豆包聊天网站的页面对象模型 - 使用浏览器原生 EventSource API

包含豆包聊天界面的页面对象和元素选择器。
同时包含 Metaso.cn 网站的页面对象模型。
"""

from typing import Optional, List, Dict, Any
from playwright.async_api import Page, Locator
import asyncio
import logging
import json
import os
import re
import urllib.parse
from pathlib import Path


class DoubaoPage:
    """豆包聊天网站的页面对象。"""

    # 基于实际网站结构的元素选择器
    SELECTORS = {
        'create_conversation_button': '[data-testid="create_conversation_button"]',
        'deep_think_select_button': '[data-testid="deep_think_select_button"]',
        'deep_think_select_item_1': '[data-testid="deep_think_select_item_1"][value="1"]',
        'chat_input': '[data-testid="chat_input_input"]',
        'chat_messages': '[data-testid="chat-message"], .message, .chat-message',
        'thinking_process': '[data-testid="thinking"], .thinking, .reasoning',
        'chat_response': '[data-testid="response"], .response, .chat-response',
        'loading_indicator': '.loading, .spinner, [data-testid="loading"]',
        'error_message': '.error, .alert-error, [data-testid="error"]',
        'new_chat_button': 'button:has-text("新对话"), [data-testid="new-chat"]',
        'chat_container': '.chat-container, .conversation, [data-testid="chat-container"]'
    }

    def __init__(self, page: Page):
        """
        初始化页面对象。

        参数:
            page: Playwright页面实例
        """
        self.page = page
        self.logger = logging.getLogger(__name__)

        # 存储捕获的响应
        self.chat_responses = []
        self.current_response_data = []
        self.response_complete = False
        self.debug_mode = True  # 启用调试日志
        self.all_responses = []  # 存储所有响应用于调试

        # 设置网络请求监控
        self._setup_network_monitoring()

    def _setup_network_monitoring(self) -> None:
        """为SSE流设置浏览器端EventSource监控。"""
        try:
            # 注意：EventSource监控脚本将在导航到页面时注入
            self.logger.info("浏览器端EventSource监控设置已初始化")
        except Exception as e:
            self.logger.error(f"设置EventSource监控失败: {e}")

    async def _inject_eventsource_monitor(self) -> None:
        """向浏览器注入EventSource监控脚本。"""
        try:
            # 读取外部 JS 文件
            js_file_path = os.path.join(os.path.dirname(__file__), 'eventsource_monitor.js')

            with open(js_file_path, 'r', encoding='utf-8') as f:
                monitor_script = f.read()

            # 注入脚本
            await self.page.evaluate(monitor_script)

            self.logger.info("从外部文件成功注入EventSource监控脚本")

        except Exception as e:
            self.logger.error(f"注入EventSource监控脚本失败: {e}")
            # 如果外部文件加载失败，使用简化的内联脚本作为后备
            await self._inject_fallback_monitor()

    async def _inject_fallback_monitor(self) -> None:
        """注入简化的后备监控脚本"""
        try:
            await self.page.evaluate("""
                () => {
                    window.sseData = {
                        events: [],
                        isComplete: false,
                        error: null,
                        currentStream: null,
                        debugInfo: []
                    };
                    
                    window.debugLog = function(message, data) {
                        console.log('SSE Debug:', message, data);
                    };
                    
                    window.clearSSEData = function() {
                        window.sseData = {
                            events: [],
                            isComplete: false,
                            error: null,
                            currentStream: null,
                            debugInfo: []
                        };
                    };
                    
                    console.log('Fallback EventSource monitoring script loaded');
                }
            """)
            self.logger.info("Fallback EventSource monitoring script injected")
        except Exception as e:
            self.logger.error(f"Failed to inject fallback monitoring script: {e}")

    async def _get_browser_sse_data(self) -> Dict[str, Any]:
        """
        Get SSE data captured by browser-side EventSource.

        Returns:
            Dictionary containing SSE events and metadata
        """
        try:
            # Get the SSE data from browser
            sse_data = await self.page.evaluate("() => window.sseData")
            
            if sse_data:
                self.logger.info(f"Retrieved {len(sse_data.get('events', []))} SSE events from browser")
                return sse_data
            else:
                self.logger.warning("No SSE data found in browser")
                return {"events": [], "isComplete": False, "error": None}
                
        except Exception as e:
            self.logger.error(f"Error getting browser SSE data: {e}")
            return {"events": [], "isComplete": False, "error": str(e)}

    async def _clear_browser_sse_data(self) -> None:
        """清除浏览器中的SSE数据以准备新请求。"""
        try:
            await self.page.evaluate("() => { if (window.clearSSEData) window.clearSSEData(); }")
            self.logger.info("已清除浏览器SSE数据")
        except Exception as e:
            self.logger.error(f"清除浏览器SSE数据时出错: {e}")

    async def _process_browser_sse_events(self) -> None:
        """
        Process SSE events captured by browser-side EventSource.
        """
        try:
            self.logger.info("Processing browser-side SSE events")

            # Get SSE data from browser
            sse_data = await self._get_browser_sse_data()
            
            # 详细调试输出
            self.logger.info(f"Raw SSE data: {sse_data}")
            
            if sse_data.get('error'):
                self.logger.error(f"Browser SSE error: {sse_data['error']}")
                self.response_complete = True
                return

            events = sse_data.get('events', [])
            self.logger.info(f"Processing {len(events)} SSE events from browser")

            # Reset response data for new request
            self.current_response_data = []
            self.response_complete = False

            # Process each event with detailed logging
            for event_idx, event in enumerate(events):
                self.logger.info(f"Event {event_idx + 1} raw data: {event}")
                
                if event:
                    # 尝试多种数据提取方式
                    event_data = None
                    
                    # 方式1：直接使用 event.data
                    if 'data' in event:
                        event_data = event['data']
                        self.logger.info(f"Event {event_idx + 1} extracted via 'data': {event_data}")
                    
                    # 方式2：直接使用整个 event
                    elif event:
                        event_data = event
                        self.logger.info(f"Event {event_idx + 1} using whole event: {event_data}")
                    
                    if event_data:
                        self.current_response_data.append(event_data)
                        self.logger.info(f"Event {event_idx + 1} added to response data")
                    else:
                        self.logger.warning(f"Event {event_idx + 1} has no usable data")

            # Check if stream is complete
            self.response_complete = sse_data.get('isComplete', False)
            
            self.logger.info(f"Browser SSE events processed: {len(self.current_response_data)} events, complete: {self.response_complete}")
            self.logger.info(f"Final current_response_data: {self.current_response_data}")

        except Exception as e:
            self.logger.error(f"Error processing browser SSE events: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")
            self.response_complete = True

    async def get_latest_chat_response(self) -> Dict[str, Any]:
        """
        Get the latest chat completion response data.

        Returns:
            Dictionary containing the response data
        """
        try:
            if not self.current_response_data:
                return {"error": "No response data available"}

            # 尝试获取JavaScript端处理后的内容
            js_processed_content = await self._get_js_processed_content()

            # Combine all response events into a structured format
            combined_response = {
                "events": self.current_response_data,
                "event_count": len(self.current_response_data),
                "response_complete": self.response_complete,
                "full_text": self._extract_full_text_from_events(),
                "thinking_content": self._extract_thinking_from_events(),
                "final_answer": self._extract_final_answer_from_events(),
                # 新增：JavaScript端处理后的内容
                "js_processed_content": js_processed_content
            }

            self.logger.info(f"Retrieved chat response with {len(self.current_response_data)} events")
            if js_processed_content.get('thinking_content'):
                self.logger.info(f"JavaScript processed thinking content: {js_processed_content['thinking_content'][:100]}...")
            if js_processed_content.get('chat_content'):
                self.logger.info(f"JavaScript processed chat content: {js_processed_content['chat_content'][:100]}...")

            return combined_response

        except Exception as e:
            self.logger.error(f"Error getting latest chat response: {e}")
            return {"error": f"Failed to get response: {e}"}

    async def _get_js_processed_content(self) -> Dict[str, Any]:
        """获取JavaScript端处理后的内容"""
        try:
            # 尝试获取JavaScript端的处理结果
            processed_content = await self.page.evaluate("() => window.getProcessedContent ? window.getProcessedContent() : null")
            thinking_fragments = await self.page.evaluate("() => window.getThinkingFragments ? window.getThinkingFragments() : []")
            chat_fragments = await self.page.evaluate("() => window.getChatFragments ? window.getChatFragments() : []")
            thinking_content = await self.page.evaluate("() => window.getThinkingContent ? window.getThinkingContent() : ''")
            chat_content = await self.page.evaluate("() => window.getChatContent ? window.getChatContent() : ''")

            self.logger.info(f"JavaScript processing results: thinking_fragments={len(thinking_fragments)}, chat_fragments={len(chat_fragments)}")

            return {
                "processed_content": processed_content,
                "thinking_fragments": thinking_fragments,
                "chat_fragments": chat_fragments,
                "thinking_content": thinking_content,
                "chat_content": chat_content,
                "has_js_processing": True
            }
        except Exception as e:
            self.logger.debug(f"JavaScript processing not available: {e}")
            return {
                "processed_content": None,
                "thinking_fragments": [],
                "chat_fragments": [],
                "thinking_content": "",
                "chat_content": "",
                "has_js_processing": False
            }

    def _extract_full_text_from_events(self) -> str:
        """从浏览器捕获的SSE事件中提取完整文本内容。"""
        full_text = ""

        try:
            self.logger.info(f"从{len(self.current_response_data)}个事件中提取文本")
            
            for event_idx, event in enumerate(self.current_response_data):
                self.logger.info(f"Processing event {event_idx + 1}: {type(event)} - {str(event)[:200]}...")
                
                if isinstance(event, dict):
                    # 处理嵌套的数据结构
                    extracted_text = self._extract_text_from_nested_data(event)
                    if extracted_text:
                        full_text += extracted_text + " "
                        
                elif isinstance(event, str):
                    full_text += event + " "

            self.logger.info(f"Extracted full text length: {len(full_text)} characters")
            if full_text:
                self.logger.info(f"Full text preview: {full_text[:200]}...")

        except Exception as e:
            self.logger.error(f"Error extracting full text: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")

        return full_text.strip()

    def _extract_text_from_nested_data(self, data: dict) -> str:
        """从嵌套的数据结构中提取文本内容"""
        text_parts = []

        try:
            # 方法1: 处理 message.content 中的 JSON 字符串
            if 'message' in data and isinstance(data['message'], dict):
                message_data = data['message']
                if 'content' in message_data:
                    content_str = str(message_data['content'])

                    # 尝试解析 JSON 内容
                    try:
                        # 清理转义字符
                        cleaned_content = self._clean_json_string(content_str)
                        content_data = json.loads(cleaned_content)

                        # 递归提取文本内容
                        nested_text = self._extract_text_from_nested_data(content_data)
                        if nested_text:
                            text_parts.append(nested_text)

                    except json.JSONDecodeError:
                        # 如果不是 JSON，直接使用
                        text_parts.append(content_str)

            # 方法2: 直接的文本字段
            direct_fields = ['text', 'content', 'answer', 'response']
            for field in direct_fields:
                if field in data and data[field] and field != 'content':  # 避免重复处理 message.content
                    text_content = str(data[field])
                    # 清理可能的转义字符
                    cleaned_text = self._clean_json_string(text_content)
                    text_parts.append(cleaned_text)

            # 方法3: 处理 references 结构
            if 'references' in data and isinstance(data['references'], list):
                for ref in data['references']:
                    if isinstance(ref, dict) and 'data' in ref:
                        ref_data = ref['data']
                        if isinstance(ref_data, list):
                            for item in ref_data:
                                if isinstance(item, dict):
                                    # 提取 text_card.summary
                                    if 'text_card' in item and 'summary' in item['text_card']:
                                        text_parts.append(item['text_card']['summary'])
                                    # 提取其他文本字段
                                    for field in ['title', 'content', 'text', 'summary']:
                                        if field in item and item[field]:
                                            text_parts.append(str(item[field]))

            # 方法4: 递归处理嵌套对象（跳过已处理的字段）
            for key, value in data.items():
                if key not in ['message', 'references']:  # 避免重复处理
                    if isinstance(value, dict):
                        nested_text = self._extract_text_from_nested_data(value)
                        if nested_text:
                            text_parts.append(nested_text)
                    elif isinstance(value, list):
                        for item in value:
                            if isinstance(item, dict):
                                nested_text = self._extract_text_from_nested_data(item)
                                if nested_text:
                                    text_parts.append(nested_text)
                            elif isinstance(item, str) and item.strip():
                                text_parts.append(item)

        except Exception as e:
            self.logger.error(f"Error extracting text from nested data: {e}")

        # 清理所有文本部分的转义字符
        cleaned_parts = []
        for part in text_parts:
            cleaned_part = self._clean_json_string(str(part))
            if cleaned_part.strip():
                cleaned_parts.append(cleaned_part)

        return " ".join(cleaned_parts)

    def _extract_thinking_from_events(self) -> List[str]:
        """从浏览器捕获的SSE事件中提取思考过程内容。"""
        thinking_content = []

        try:
            self.logger.info(f"从{len(self.current_response_data)}个事件中提取思考内容")

            for event_idx, event in enumerate(self.current_response_data):
                self.logger.info(f"Checking event {event_idx + 1} for thinking content")

                if isinstance(event, dict):
                    # 提取思考内容
                    thinking_text = self._extract_thinking_from_nested_data(event)
                    if thinking_text:
                        thinking_content.extend(thinking_text)

            self.logger.info(f"Extracted {len(thinking_content)} thinking items")

        except Exception as e:
            self.logger.error(f"Error extracting thinking content: {e}")
            import traceback
            self.logger.error(f"Traceback: {traceback.format_exc()}")

        return thinking_content

    def _extract_thinking_from_nested_data(self, data: dict) -> List[str]:
        """从嵌套的数据结构中提取思考内容"""
        thinking_parts = []

        try:
            # 方法1: 直接的思考字段
            thinking_fields = ['think', 'thinking', 'reasoning', 'analysis', 'thought', 'cbatchat']
            for field in thinking_fields:
                if field in data and data[field]:
                    thinking_text = str(data[field])
                    if thinking_text.strip():
                        self.logger.info(f"Found thinking content in field '{field}': {thinking_text[:100]}...")
                        thinking_parts.append(thinking_text)

            # 方法2: 检查 message 结构中的思考内容
            if 'message' in data and isinstance(data['message'], dict):
                message_data = data['message']

                # 检查 message.content 中的 JSON 字符串
                if 'content' in message_data:
                    content_str = str(message_data['content'])

                    # 尝试解析 JSON 内容
                    try:
                        # 清理转义字符
                        cleaned_content = self._clean_json_string(content_str)
                        content_data = json.loads(cleaned_content)

                        # 递归提取思考内容
                        nested_thinking = self._extract_thinking_from_nested_data(content_data)
                        thinking_parts.extend(nested_thinking)

                    except json.JSONDecodeError:
                        # 如果不是 JSON，检查是否包含思考关键词
                        thinking_indicators = ['思考', 'thinking', '分析', 'analysis', '推理', 'reasoning']
                        if any(indicator in content_str.lower() for indicator in thinking_indicators):
                            thinking_parts.append(content_str)

            # 方法3: 检查是否包含思考关键词的文本字段
            text_fields = ['text', 'content', 'data']
            for field in text_fields:
                if field in data and data[field]:
                    text = str(data[field])
                    # 检查是否包含思考相关的关键词
                    thinking_indicators = ['思考', 'thinking', '分析', 'analysis', '推理', 'reasoning', '考虑', 'consider']
                    if any(indicator in text.lower() for indicator in thinking_indicators):
                        if len(text.strip()) > 10:  # 过滤太短的文本
                            self.logger.info(f"Found thinking indicator in '{field}': {text[:100]}...")
                            thinking_parts.append(text)

            # 方法4: 递归处理嵌套对象
            for key, value in data.items():
                if isinstance(value, dict) and key not in ['message']:  # 避免重复处理 message
                    nested_thinking = self._extract_thinking_from_nested_data(value)
                    thinking_parts.extend(nested_thinking)
                elif isinstance(value, list):
                    for item in value:
                        if isinstance(item, dict):
                            nested_thinking = self._extract_thinking_from_nested_data(item)
                            thinking_parts.extend(nested_thinking)

        except Exception as e:
            self.logger.error(f"Error extracting thinking from nested data: {e}")

        return thinking_parts

    def _clean_json_string(self, json_str: str) -> str:
        """清理 JSON 字符串中的多余转义字符"""
        try:
            # 移除多余的转义斜杠
            cleaned = json_str

            # 处理多层转义的情况
            while '\\\\' in cleaned:
                cleaned = cleaned.replace('\\\\', '\\')

            # 处理转义的引号
            cleaned = cleaned.replace('\\"', '"')

            return cleaned
        except Exception as e:
            self.logger.error(f"Error cleaning JSON string: {e}")
            return json_str

    def _extract_final_answer_from_events(self) -> str:
        """从浏览器捕获的SSE事件中提取最终答案。"""
        try:
            if self.current_response_data:
                # 尝试获取最后的实质内容
                for event in reversed(self.current_response_data[-5:]):
                    if isinstance(event, dict):
                        text = str(event.get('text', '') or event.get('content', ''))
                        if len(text.strip()) > 50:
                            return text.strip()

                # 回退：返回最后事件的文本
                last_event = self.current_response_data[-1]
                if isinstance(last_event, dict):
                    text = str(last_event.get('text', '') or last_event.get('content', ''))
                    return text

        except Exception as e:
            self.logger.error(f"提取最终答案时出错: {e}")

        return ""

    async def navigate_to_chat(self) -> None:
        """导航到豆包聊天页面并确保EventSource监控处于活动状态。"""
        try:
            await self.page.goto("https://www.doubao.com/chat/", wait_until="networkidle")
            await self.page.wait_for_load_state("domcontentloaded")

            # 导航后重新注入EventSource监控脚本
            await self._inject_eventsource_monitor()

            # 处理页面加载后可能出现的模态对话框
            await self._handle_modal_dialog()

            self.logger.info("成功导航到豆包聊天页面并启用EventSource监控")
        except Exception as e:
            self.logger.error(f"导航到聊天页面失败: {e}")
            raise

    async def wait_for_page_ready(self, timeout: int = 130000) -> bool:
        """
        Wait for the page to be ready for interaction.

        Args:
            timeout: Maximum time to wait in milliseconds

        Returns:
            True if page is ready, False otherwise
        """
        try:
            # Check for and handle modal dialog first
            await self._handle_modal_dialog()

            # Wait for deep think select button to be visible
            await self.page.wait_for_selector(
                self.SELECTORS['deep_think_select_button'],
                state="visible",
                timeout=timeout
            )
            return True
        except Exception as e:
            self.logger.error(f"Page not ready within timeout: {e}")
            return False

    async def _handle_modal_dialog(self) -> bool:
        """
        Handle modal dialog (#dialog-0) if it appears on the page.
        Clicks outside the dialog to dismiss it.

        Returns:
            True if dialog was handled (or not present), False if error
        """
        try:
            # Check if dialog exists
            dialog_selector = '#dialog-0'
            dialog_exists = await self.page.locator(dialog_selector).count() > 0

            if dialog_exists:
                self.logger.info("Modal dialog (#dialog-0) detected, attempting to dismiss")

                # Wait a moment for the dialog to fully appear
                await self.page.wait_for_timeout(500)

                # Get page dimensions to click outside the dialog
                viewport = self.page.viewport_size
                if viewport:
                    # Click in the top-left corner (usually safe area outside dialogs)
                    click_x = 50
                    click_y = 50

                    # Perform the click outside the dialog
                    await self.page.mouse.click(click_x, click_y)
                    self.logger.info(f"Clicked outside dialog at coordinates ({click_x}, {click_y})")

                    # Wait for dialog to disappear
                    try:
                        await self.page.wait_for_selector(dialog_selector, state="detached", timeout=5000)
                        self.logger.info("Modal dialog successfully dismissed")

                        # Add a small delay to ensure page is stable after dialog dismissal
                        await self.page.wait_for_timeout(1000)

                    except Exception:
                        # If dialog doesn't disappear, try clicking elsewhere
                        self.logger.warning("Dialog didn't disappear with first click, trying alternative location")

                        # Try clicking in bottom-right corner
                        click_x = viewport['width'] - 50
                        click_y = viewport['height'] - 50
                        await self.page.mouse.click(click_x, click_y)

                        # Wait again for dialog to disappear
                        await self.page.wait_for_selector(dialog_selector, state="detached", timeout=3000)
                        self.logger.info("Modal dialog dismissed with second attempt")

                        await self.page.wait_for_timeout(1000)
                else:
                    self.logger.warning("Could not get viewport size, using default click location")
                    await self.page.mouse.click(100, 100)
                    await self.page.wait_for_timeout(2000)
            else:
                self.logger.debug("No modal dialog detected")

            return True

        except Exception as e:
            self.logger.warning(f"Error handling modal dialog: {e}")
            # Don't fail the entire process if dialog handling fails
            return True

    async def click_deep_think_select(self, timeout: int = 10000) -> bool:
        """点击深度思考选择按钮。"""
        try:
            deep_think_button = self.page.locator(self.SELECTORS['deep_think_select_button'])
            await deep_think_button.wait_for(state="visible", timeout=timeout)
            await deep_think_button.click()
            await asyncio.sleep(1)
            self.logger.info("成功点击深度思考选择按钮")
            return True
        except Exception as e:
            self.logger.error(f"点击深度思考选择按钮失败: {e}")
            return False

    async def select_deep_think_item_1(self, timeout: int = 10000) -> bool:
        """选择深度思考项目1选项。"""
        try:
            item_1 = self.page.locator(self.SELECTORS['deep_think_select_item_1'])
            await item_1.wait_for(state="visible", timeout=timeout)
            await item_1.click()
            await asyncio.sleep(2)
            self.logger.info("成功选择深度思考项目1")
            return True
        except Exception as e:
            self.logger.error(f"选择深度思考项目1失败: {e}")
            return False

    async def send_message_with_enter(self, message: str, timeout: int = 10000) -> bool:
        """通过输入并按回车键在聊天中发送消息。"""
        try:
            chat_input = self.page.locator(self.SELECTORS['chat_input'])
            await chat_input.wait_for(state="visible", timeout=timeout)
            await chat_input.clear()
            await chat_input.fill(message)
            await chat_input.press("Enter")
            self.logger.info(f"使用回车发送消息: {message[:50]}...")
            return True
        except Exception as e:
            self.logger.error(f"使用回车发送消息失败: {e}")
            return False

    async def wait_for_chat_completion(self, timeout: int = 60000) -> bool:
        """使用浏览器端EventSource监控等待聊天完成。"""
        try:
            self.logger.info("等待浏览器端SSE完成...")

            timeout_seconds = timeout / 1000
            elapsed = 0
            check_interval = 0.5

            while elapsed < timeout_seconds:
                sse_data = await self._get_browser_sse_data()

                if sse_data.get('isComplete') and sse_data.get('events'):
                    self.logger.info(f"在{elapsed:.1f}秒后检测到浏览器SSE完成")
                    await self._process_browser_sse_events()
                    return True

                await asyncio.sleep(check_interval)
                elapsed += check_interval

            self.logger.warning(f"浏览器SSE完成超时，已等待{timeout}毫秒")

            # 检查即使未标记为完成是否有一些数据
            sse_data = await self._get_browser_sse_data()
            if sse_data.get('events'):
                self.logger.info("尽管超时但发现SSE事件，正在处理...")
                await self._process_browser_sse_events()
                return True

            return False

        except Exception as e:
            self.logger.error(f"等待浏览器SSE完成时出错: {e}")
            return False

    async def setup_conversation_and_send_message(self, message: str, wait_for_completion: bool = True) -> Dict[str, Any]:
        """完整的设置流程：选择深度思考、发送消息并捕获响应。"""
        try:
            self.logger.info("开始使用浏览器端SSE捕获的对话设置工作流程")

            # Step 1: Click deep think select button
            if not await self.click_deep_think_select():
                return {"success": False, "error": "Failed to select deep think", "response_data": None}

            # Step 2: Select deep think item 1
            if not await self.select_deep_think_item_1():
                return {"success": False, "error": "Failed to select deep think item 1", "response_data": None}

            # Step 3: Clear browser SSE data before sending message
            await self._clear_browser_sse_data()

            # Step 4: Send message with Enter
            if not await self.send_message_with_enter(message):
                return {"success": False, "error": "Failed to send message", "response_data": None}

            # Step 5: Wait for and capture SSE response
            if wait_for_completion:
                response_received = await self.wait_for_chat_completion(timeout=360000)

                if response_received:
                    response_data = await self.get_latest_chat_response()
                    return {
                        "success": True,
                        "error": None,
                        "response_data": response_data,
                        "message_sent": message[:100] + "..." if len(message) > 100 else message
                    }
                else:
                    return {
                        "success": True,
                        "error": "SSE timeout",
                        "response_data": None,
                        "message_sent": message[:100] + "..." if len(message) > 100 else message
                    }
            else:
                return {
                    "success": True,
                    "error": None,
                    "response_data": None,
                    "message_sent": message[:100] + "..." if len(message) > 100 else message
                }

        except Exception as e:
            self.logger.error(f"Conversation setup workflow failed: {e}")
            return {"success": False, "error": str(e), "response_data": None}


class MetasoPage:
    """Metaso.cn 网站的页面对象。"""

    # Metaso.cn 网站的元素选择器
    SELECTORS = {
        'search_textarea': '.search-consult-textarea',
        'search_button': 'button[type="submit"]',
        'search_form': 'form',
        'loading_indicator': '.loading, .spinner',
        'error_message': '.error, .alert-error',
        'content_container': '.content, .main-content'
    }

    # Metaso.cn 的 URL 模式
    BASE_URL = "https://metaso.cn/"
    SEARCH_URL_PATTERN = r"https://metaso\.cn/search/([a-zA-Z0-9\-]+)\?q=(.+)"
    API_URL_PATTERN = "https://metaso.cn/api/session/{session_id}"

    def __init__(self, page: Page, login_config: Optional[Dict[str, str]] = None):
        """
        初始化 Metaso 页面对象。

        参数:
            page: Playwright页面实例
            login_config: 登录配置字典，包含 username 和 password
        """
        self.page = page
        self.logger = logging.getLogger(__name__)

        # 存储会话信息
        self.session_id: Optional[str] = None
        self.search_query: Optional[str] = None
        self.api_responses = []
        self.captured_data = None

        # 登录配置
        self.login_config = login_config or {}

        # Cookie 文件路径
        self.cookie_file = Path("metaso_cookies.json")

        # 设置网络监控
        self._setup_network_monitoring()

    async def save_cookies(self) -> None:
        """保存当前页面的 cookies 到文件。"""
        try:
            cookies = await self.page.context.cookies()
            with open(self.cookie_file, 'w', encoding='utf-8') as f:
                json.dump(cookies, f, ensure_ascii=False, indent=2)
            self.logger.info(f"Cookies 已保存到 {self.cookie_file}")
        except Exception as e:
            self.logger.error(f"保存 cookies 失败: {e}")

    async def load_cookies(self) -> bool:
        """从文件加载 cookies 到当前页面。"""
        try:
            if not self.cookie_file.exists():
                self.logger.info("Cookie 文件不存在，跳过加载")
                return False

            with open(self.cookie_file, 'r', encoding='utf-8') as f:
                cookies = json.load(f)

            await self.page.context.add_cookies(cookies)
            self.logger.info(f"已从 {self.cookie_file} 加载 cookies")
            return True
        except Exception as e:
            self.logger.error(f"加载 cookies 失败: {e}")
            return False

    async def check_login_status(self) -> bool:
        """检查当前是否已登录（通过检查页面是否有登录按钮）。"""
        try:
            self.logger.info("检查登录状态...")

            # 等待页面加载完成
            await asyncio.sleep(2)

            # 查找登录/注册按钮
            login_button = self.page.locator('button:has-text("登录/注册")')

            # 检查登录按钮是否存在
            button_count = await login_button.count()

            if button_count > 0:
                self.logger.info("发现登录按钮，用户未登录")
                return False
            else:
                self.logger.info("未发现登录按钮，用户可能已登录")
                return True

        except Exception as e:
            self.logger.error(f"检查登录状态失败: {e}")
            return False

    async def clear_cookies(self) -> None:
        """清除保存的 cookies 文件。"""
        try:
            if self.cookie_file.exists():
                self.cookie_file.unlink()
                self.logger.info("已清除保存的 cookies")
        except Exception as e:
            self.logger.error(f"清除 cookies 失败: {e}")

    def _setup_network_monitoring(self) -> None:
        """设置网络请求监控以捕获 API 响应。"""
        try:
            # 监控网络响应
            self.page.on("response", self._handle_response)
            self.logger.info("Metaso 网络监控设置完成")
        except Exception as e:
            self.logger.error(f"设置网络监控失败: {e}")

    async def _handle_response(self, response) -> None:
        """处理网络响应，捕获 session API 调用。"""
        try:
            url = response.url

            # 检查是否是我们感兴趣的 API 端点（更灵活的匹配）
            if "/api/session/" in url and self.session_id and self.session_id in url:
                self.logger.info(f"捕获到 session API 响应: {url}")

                try:
                    # 获取响应数据
                    response_data = await response.json()
                    self.api_responses.append({
                        "url": url,
                        "status": response.status,
                        "data": response_data,
                        "timestamp": asyncio.get_event_loop().time()
                    })

                    # 记录响应数据结构用于调试
                    self.logger.info(f"API 响应数据结构: {list(response_data.keys()) if isinstance(response_data, dict) else type(response_data)}")

                    # 提取目标数据路径
                    if (response_data and
                        "data" in response_data and
                        "results" in response_data["data"] and
                        len(response_data["data"]["results"]) > 0 and
                        "normalAnswer" in response_data["data"]["results"][0] and
                        "steps" in response_data["data"]["results"][0]["normalAnswer"]):

                        self.captured_data = response_data["data"]["results"][0]["normalAnswer"]["steps"]
                        self.logger.info(f"成功提取 steps 数据: {len(self.captured_data) if isinstance(self.captured_data, list) else 'N/A'} 项")
                    else:
                        # 记录数据结构以便调试
                        if response_data and "data" in response_data:
                            data_keys = list(response_data["data"].keys()) if isinstance(response_data["data"], dict) else "非字典类型"
                            self.logger.info(f"响应数据中的 data 字段结构: {data_keys}")

                            if "results" in response_data["data"] and response_data["data"]["results"]:
                                result_keys = list(response_data["data"]["results"][0].keys()) if isinstance(response_data["data"]["results"][0], dict) else "非字典类型"
                                self.logger.info(f"第一个结果的结构: {result_keys}")
                        else:
                            self.logger.info(f"响应数据不包含预期的 data 字段，实际结构: {list(response_data.keys()) if isinstance(response_data, dict) else type(response_data)}")

                except Exception as e:
                    self.logger.error(f"解析 API 响应失败: {e}")

        except Exception as e:
            self.logger.error(f"处理响应时出错: {e}")

    async def navigate_to_metaso(self) -> None:
        """导航到 Metaso.cn 主页并处理登录。"""
        try:
            self.logger.info(f"导航到 Metaso: {self.BASE_URL}")

            # 尝试加载保存的 cookies
            cookies_loaded = await self.load_cookies()

            # 导航到页面
            await self.page.goto(self.BASE_URL, wait_until="networkidle", timeout=300000)

            # 检查登录状态
            is_logged_in = await self.check_login_status()

            if is_logged_in:
                self.logger.info("用户已登录，跳过登录流程")
            elif self.login_config.get('username') and self.login_config.get('password'):
                self.logger.info("用户未登录，开始登录流程")
                await self.perform_login()

                # 登录成功后保存 cookies
                await self.save_cookies()
            else:
                self.logger.info("未配置登录信息且用户未登录，跳过登录流程")

            await self.wait_for_page_ready()
            self.logger.info("成功导航到 Metaso 主页")
        except Exception as e:
            self.logger.error(f"导航到 Metaso 失败: {e}")
            raise

    async def wait_for_page_ready(self, timeout: int = 10000) -> None:
        """等待页面完全加载。"""
        try:
            # 等待搜索框出现
            search_textarea = self.page.locator(self.SELECTORS['search_textarea'])
            await search_textarea.wait_for(state="visible", timeout=timeout)

            # 等待页面稳定
            await asyncio.sleep(2)

            self.logger.info("页面已准备就绪")
        except Exception as e:
            self.logger.error(f"等待页面准备就绪失败: {e}")
            raise

    async def perform_login(self) -> None:
        """执行登录流程。"""
        try:
            self.logger.info("开始执行登录流程")

            # 步骤 1: 检查并点击展开按钮
            await self._click_expand_button_if_exists()

            # 步骤 2: 查找并点击登录/注册按钮
            await self._click_login_register_button()

            # 步骤 3: 在弹出的模态框中点击账号密码选项
            await self._click_account_password_option()

            # 步骤 4: 输入账号和密码
            await self._input_credentials()

            # 步骤 5: 勾选政策复选框
            await self._check_policy_checkbox()

            # 步骤 6: 点击登录按钮
            await self._click_login_submit()

            # 等待登录完成
            await asyncio.sleep(3)

            # 验证登录是否成功
            login_success = await self.check_login_status()
            if login_success:
                self.logger.info("登录成功")
                # 保存 cookies
                await self.save_cookies()
            else:
                self.logger.warning("登录可能失败，请检查用户名和密码")

            self.logger.info("登录流程完成")

        except Exception as e:
            self.logger.error(f"登录流程失败: {e}")
            raise

    async def _click_expand_button_if_exists(self) -> None:
        """检查并点击展开按钮（如果存在）。"""
        try:
            self.logger.info("检查是否存在展开按钮")

            # 查找 aria-label="展开" 的按钮
            expand_button = self.page.locator('button[aria-label="展开"]')

            # 检查按钮是否存在且可见
            if await expand_button.count() > 0:
                await expand_button.wait_for(state="visible", timeout=5000)
                await expand_button.click()
                self.logger.info("点击了展开按钮")
                await asyncio.sleep(1)
            else:
                self.logger.info("未找到展开按钮，跳过此步骤")

        except Exception as e:
            self.logger.warning(f"处理展开按钮时出错，继续执行: {e}")

    async def _click_login_register_button(self) -> None:
        """查找并点击登录/注册按钮。"""
        try:
            self.logger.info("查找登录/注册按钮")

            # 查找内容为"登录/注册"的按钮
            login_button = self.page.locator('button:has-text("登录/注册")')

            await login_button.wait_for(state="visible", timeout=10000)
            await login_button.click()
            self.logger.info("点击了登录/注册按钮")

            # 等待模态框出现
            await asyncio.sleep(2)

        except Exception as e:
            self.logger.error(f"点击登录/注册按钮失败: {e}")
            raise

    async def _click_account_password_option(self) -> None:
        """在模态框中点击账号密码选项。"""
        try:
            self.logger.info("查找账号密码选项")

            # 等待模态框出现
            modal = self.page.locator('.MuiModal-root')
            await modal.wait_for(state="visible", timeout=10000)

            # 在模态框中查找包含"账号密码"文本的 .MuiBox-root 元素
            account_password_option = modal.locator('.MuiBox-root:has-text("账号密码")')

            await account_password_option.wait_for(state="visible", timeout=10000)
            await account_password_option.click()
            self.logger.info("点击了账号密码选项")

            # 等待登录表单出现
            await asyncio.sleep(2)

        except Exception as e:
            self.logger.error(f"点击账号密码选项失败: {e}")
            raise

    async def _input_credentials(self) -> None:
        """输入账号和密码。"""
        try:
            self.logger.info("输入登录凭据")

            username = self.login_config.get('username')
            password = self.login_config.get('password')

            if not username or not password:
                raise ValueError("登录配置中缺少用户名或密码")

            # 输入账号
            account_input = self.page.locator('#desktop-login-account')
            await account_input.wait_for(state="visible", timeout=10000)
            await account_input.clear()
            await account_input.fill(username)
            self.logger.info("已输入账号")

            # 输入密码
            password_input = self.page.locator('#desktop-login-pwd')
            await password_input.wait_for(state="visible", timeout=10000)
            await password_input.clear()
            await password_input.fill(password)
            self.logger.info("已输入密码")

        except Exception as e:
            self.logger.error(f"输入登录凭据失败: {e}")
            raise

    async def _check_policy_checkbox(self) -> None:
        """勾选政策复选框。"""
        try:
            self.logger.info("勾选政策复选框")

            policy_checkbox = self.page.locator('#desktop-login-policy')
            await policy_checkbox.wait_for(state="visible", timeout=10000)

            # 检查是否已经勾选
            if not await policy_checkbox.is_checked():
                await policy_checkbox.check()
                self.logger.info("已勾选政策复选框")
            else:
                self.logger.info("政策复选框已经勾选")

        except Exception as e:
            self.logger.error(f"勾选政策复选框失败: {e}")
            raise

    async def _click_login_submit(self) -> None:
        """点击登录提交按钮。"""
        try:
            self.logger.info("点击登录提交按钮")

            submit_button = self.page.locator('button[type="submit"]')
            await submit_button.wait_for(state="visible", timeout=10000)
            await submit_button.click()
            self.logger.info("已点击登录提交按钮")

            # 等待登录处理
            await asyncio.sleep(3)

        except Exception as e:
            self.logger.error(f"点击登录提交按钮失败: {e}")
            raise

    async def input_search_query(self, query: str) -> None:
        """在搜索框中输入查询内容。"""
        try:
            self.search_query = query
            self.logger.info(f"输入搜索查询: {query}")

            # 使用 locator 定位搜索框
            self.search_textarea = self.page.locator(self.SELECTORS['search_textarea'])

            # 等待搜索框可见
            await self.search_textarea.wait_for(state="visible", timeout=10000)

            # 清空并输入查询
            await self.search_textarea.clear()
            await self.search_textarea.fill(query)

            # 模拟人类输入延迟
            await asyncio.sleep(1)

            self.logger.info("搜索查询输入完成")
        except Exception as e:
            self.logger.error(f"输入搜索查询失败: {e}")
            raise

    async def submit_search(self) -> None:
        """提交搜索查询。"""
        try:
            self.logger.info("提交搜索查询")

            # 确保我们有搜索框的引用
            if not hasattr(self, 'search_textarea'):
                self.search_textarea = self.page.locator(self.SELECTORS['search_textarea'])

            # 在搜索框上按 Enter 键提交
            await self.search_textarea.press("Enter")
            self.logger.info(f"输入查询并按回车键提交: {self.search_query[:50]}...")

            # 等待页面开始跳转
            await asyncio.sleep(5)

            # 等待页面跳转完成
            await self.page.wait_for_load_state("networkidle", timeout=300000)

            # 提取 session ID
            current_url = self.page.url
            self.logger.info(f"搜索后的 URL: {current_url}")

            # 使用正则表达式提取 session ID
            match = re.search(self.SEARCH_URL_PATTERN, current_url)
            if match:
                self.session_id = match.group(1)
                encoded_query = match.group(2)
                decoded_query = urllib.parse.unquote(encoded_query)
                self.logger.info(f"提取到 session ID: {self.session_id}")
                self.logger.info(f"解码后的查询: {decoded_query}")
            else:
                self.logger.warning(f"无法从 URL 提取 session ID: {current_url}")

        except Exception as e:
            self.logger.error(f"提交搜索失败: {e}")
            raise

    async def wait_for_navigation_and_network_idle(self, timeout: int = 600000) -> None:
        """
        等待页面跳转完成并等待所有网络请求完成。

        参数:
            timeout: 超时时间（毫秒），默认30秒
        """
        try:
            self.logger.info("等待页面跳转完成并等待所有网络请求完成...")

            # 等待页面加载完成，networkidle 表示网络空闲（所有请求完成）
            await self.page.wait_for_load_state("networkidle", timeout=timeout)

            self.logger.info("页面跳转和网络请求已完成")

        except Exception as e:
            self.logger.warning(f"等待页面跳转和网络空闲时出现异常: {e}")
            # 如果等待网络空闲失败，至少等待页面DOM加载完成
            try:
                await self.page.wait_for_load_state("domcontentloaded", timeout=10000)
                self.logger.info("页面DOM加载完成（网络空闲等待失败，使用备用方案）")
            except Exception as e2:
                self.logger.warning(f"备用等待方案也失败: {e2}")
                # 最后的备用方案：简单等待
                await asyncio.sleep(3)
                self.logger.info("使用固定等待时间作为最后备用方案")

    async def refresh_and_wait_for_api(self, max_retries: int = 3, base_timeout: int = 3000) -> Dict[str, Any]:
        """
        刷新页面并等待 API 响应，支持重试机制。

        参数:
            max_retries: 最大重试次数，默认3次
            base_timeout: 基础等待时间（毫秒），默认3秒，每次重试延长2秒
        """
        try:
            self.logger.info(f"开始刷新页面并等待 API 响应，最大重试次数: {max_retries}")

            for attempt in range(max_retries + 1):  # +1 因为包含第一次尝试
                attempt_num = attempt + 1
                current_timeout = base_timeout + (attempt * 2000)  # 每次重试延长2秒

                self.logger.info(f"第 {attempt_num} 次尝试，等待时间: {current_timeout/1000}秒")

                # 清空之前的响应
                self.api_responses.clear()
                self.captured_data = None

                # 刷新页面
                await self.page.reload(wait_until="networkidle", timeout=300000)
                self.logger.info("页面刷新完成，开始等待 API 响应")

                # 等待 API 响应
                start_time = asyncio.get_event_loop().time()
                while (asyncio.get_event_loop().time() - start_time) < (current_timeout / 1000):
                    if self.captured_data is not None:
                        self.logger.info(f"第 {attempt_num} 次尝试成功捕获到 API 数据")
                        return {
                            "success": True,
                            "data": self.captured_data,
                            "session_id": self.session_id,
                            "api_responses": self.api_responses,
                            "attempt": attempt_num,
                            "total_attempts": attempt_num
                        }
                    await asyncio.sleep(0.5)

                # 当前尝试超时
                self.logger.warning(f"第 {attempt_num} 次尝试超时，等待了 {current_timeout/1000}秒")

                # 如果不是最后一次尝试，继续重试
                if attempt < max_retries:
                    self.logger.info(f"准备进行第 {attempt_num + 1} 次重试...")
                    await asyncio.sleep(1)  # 重试前短暂等待
                else:
                    self.logger.error(f"所有 {max_retries + 1} 次尝试都失败了")

            # 所有重试都失败
            return {
                "success": False,
                "error": f"API 响应超时，已重试 {max_retries} 次",
                "session_id": self.session_id,
                "api_responses": self.api_responses,
                "total_attempts": max_retries + 1
            }

        except Exception as e:
            self.logger.error(f"刷新页面和等待 API 失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "session_id": self.session_id,
                "api_responses": self.api_responses,
                "total_attempts": 1
            }
