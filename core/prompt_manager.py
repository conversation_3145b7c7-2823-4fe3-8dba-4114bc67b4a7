"""
豆包爬虫的提示管理器

处理提示模板的加载和处理。
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
import logging


class PromptManager:
    """管理提示模板和参数替换。"""

    def __init__(self, prompts_dir: Optional[str] = None):
        """
        初始化提示管理器。

        参数:
            prompts_dir: 包含提示文件的目录。如果为None，使用默认目录。
        """
        if prompts_dir is None:
            # 默认使用与此脚本相同父目录下的prompts目录
            self.prompts_dir = Path(__file__).parent.parent / "prompts"
        else:
            self.prompts_dir = Path(prompts_dir)

        self.logger = logging.getLogger(__name__)

        # 确保prompts目录存在
        if not self.prompts_dir.exists():
            self.logger.warning(f"提示目录未找到: {self.prompts_dir}")

    def load_prompt(self, prompt_name: str) -> str:
        """
        从文件加载提示模板。

        参数:
            prompt_name: 提示文件名（不含扩展名）

        返回:
            提示模板内容

        异常:
            FileNotFoundError: 如果提示文件不存在
        """
        prompt_file = self.prompts_dir / f"{prompt_name}.txt"

        if not prompt_file.exists():
            raise FileNotFoundError(f"提示文件未找到: {prompt_file}")

        try:
            with open(prompt_file, 'r', encoding='utf-8') as f:
                content = f.read()

            self.logger.info(f"已加载提示模板: {prompt_name}")
            return content

        except Exception as e:
            self.logger.error(f"加载提示 {prompt_name} 失败: {e}")
            raise
    
    def substitute_parameters(self, template: str, language: str, topic: str,
                            proposition_direction: str) -> str:
        """
        在提示模板中替换参数。

        参数:
            template: 提示模板内容
            language: 语言参数
            topic: 主题参数
            proposition_direction: 命题方向参数

        返回:
            替换参数后的提示
        """
        try:
            # 替换模板中的输入变量
            substituted = template.replace('[中文]', f'[{language}]')
            substituted = substituted.replace('[人工智能教育]', f'[{topic}]')
            substituted = substituted.replace('[记叙文/说明文]', f'[{proposition_direction}]')

            self.logger.info(f"已替换参数: language={language}, topic={topic}, direction={proposition_direction}")
            return substituted

        except Exception as e:
            self.logger.error(f"替换参数失败: {e}")
            return template
    
    def create_prompt(self, prompt_name: str, language: str, topic: str,
                     proposition_direction: str) -> str:
        """
        加载提示模板并替换参数。

        参数:
            prompt_name: 提示文件名（不含扩展名）
            language: 语言参数
            topic: 主题参数
            proposition_direction: 命题方向参数

        返回:
            替换参数后的完整提示
        """
        try:
            # 加载模板
            template = self.load_prompt(prompt_name)

            # 替换参数
            prompt = self.substitute_parameters(template, language, topic, proposition_direction)

            self.logger.info(f"从模板创建提示: {prompt_name}")
            return prompt

        except Exception as e:
            self.logger.error(f"创建提示失败: {e}")
            raise

    def get_ai_article_prompt(self, language: str, topic: str, proposition_direction: str) -> str:
        """
        获取替换参数后的AI文章提示。

        参数:
            language: 语言参数
            topic: 主题参数
            proposition_direction: 命题方向参数

        返回:
            完整的AI文章提示
        """
        return self.create_prompt("ai_article", language, topic, proposition_direction)
    
    def list_available_prompts(self) -> list:
        """
        列出所有可用的提示模板。

        返回:
            提示模板名称列表（不含.txt扩展名）
        """
        try:
            if not self.prompts_dir.exists():
                return []

            prompt_files = list(self.prompts_dir.glob("*.txt"))
            prompt_names = [f.stem for f in prompt_files]

            self.logger.info(f"找到 {len(prompt_names)} 个提示模板")
            return prompt_names

        except Exception as e:
            self.logger.error(f"列出提示失败: {e}")
            return []

    def validate_prompt_parameters(self, language: str, topic: str, proposition_direction: str) -> bool:
        """
        验证提示参数。

        参数:
            language: 语言参数
            topic: 主题参数
            proposition_direction: 命题方向参数

        返回:
            如果参数有效返回True，否则返回False
        """
        if not all([language, topic, proposition_direction]):
            self.logger.error("必须提供所有参数")
            return False

        if not all(isinstance(param, str) for param in [language, topic, proposition_direction]):
            self.logger.error("所有参数必须是字符串")
            return False

        if any(len(param.strip()) == 0 for param in [language, topic, proposition_direction]):
            self.logger.error("所有参数必须非空")
            return False

        return True
