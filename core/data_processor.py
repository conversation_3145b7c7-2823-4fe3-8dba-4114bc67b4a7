"""
数据处理模块

处理从豆包聊天中提取、过滤和结构化对话数据。
"""

import re
import json
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging


class DataProcessor:
    """处理和过滤来自豆包聊天的对话数据。"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据处理器。

        参数:
            config: 数据处理配置
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 用于识别和过滤UI元素的模式
        self.ui_patterns = [
            r'点击.*按钮',
            r'菜单.*选项',
            r'导航.*栏',
            r'广告.*内容',
            r'推荐.*链接',
            r'分享.*到',
            r'登录.*注册',
            r'设置.*偏好',
            r'帮助.*支持',
            r'版权.*声明',
            r'隐私.*政策',
            r'用户.*协议'
        ]

        # 用于识别思考过程的模式
        self.thinking_patterns = [
            r'思考.*过程',
            r'分析.*步骤',
            r'推理.*逻辑',
            r'考虑.*因素',
            r'评估.*选项',
            r'权衡.*利弊'
        ]
    
    def process_conversation_data(self, raw_messages: List[Dict[str, Any]],
                                language: str, topic: str,
                                proposition_direction: str) -> Dict[str, Any]:
        """
        将原始对话数据处理为结构化格式。

        参数:
            raw_messages: 从页面提取的原始消息数据
            language: 对话语言
            topic: 对话主题
            proposition_direction: 命题方向

        返回:
            结构化的对话数据
        """
        try:
            # 过滤和分类消息
            filtered_messages = self._filter_messages(raw_messages)
            thinking_processes = self._extract_thinking_processes(filtered_messages)
            chat_results = self._extract_chat_results(filtered_messages)

            # 构建最终数据结构
            structured_data = {
                "metadata": {
                    "language": language,
                    "topic": topic,
                    "proposition_direction": proposition_direction,
                    "timestamp": datetime.now().isoformat(),
                    "total_messages": len(filtered_messages),
                    "thinking_count": len(thinking_processes),
                    "chat_result_count": len(chat_results)
                },
                "thinking_processes": thinking_processes,
                "chat_results": chat_results,
                # "conversation_flow": self._build_conversation_flow(filtered_messages)
            }

            self.logger.info(f"已处理对话数据: {len(filtered_messages)} 条消息")
            return structured_data

        except Exception as e:
            self.logger.error(f"处理对话数据失败: {e}")
            return self._create_error_response(str(e))
    
    def _filter_messages(self, raw_messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        过滤UI元素和无关内容。

        参数:
            raw_messages: 原始消息数据

        返回:
            过滤后的消息
        """
        filtered = []

        for message in raw_messages:
            content = message.get('content', '')

            # 跳过空消息
            if not content.strip():
                continue

            # 如果启用过滤则跳过UI元素
            if self.config.get('exclude_ui_elements', True):
                if self._is_ui_element(content):
                    continue

            # 清理和规范化内容
            cleaned_content = self._clean_content(content)
            if cleaned_content:
                message['content'] = cleaned_content
                filtered.append(message)

        self.logger.debug(f"过滤消息 {len(raw_messages)} -> {len(filtered)}")
        return filtered
    
    def _is_ui_element(self, content: str) -> bool:
        """
        检查内容是否为UI元素。

        参数:
            content: 要检查的内容

        返回:
            如果内容是UI元素则返回True
        """
        content_lower = content.lower()

        # 检查UI模式
        for pattern in self.ui_patterns:
            if re.search(pattern, content_lower):
                return True

        # 检查常见UI特征
        if len(content) < 10 and any(char in content for char in ['>', '<', '|', '•']):
            return True

        # 检查导航类内容
        if re.search(r'^(首页|主页|返回|下一页|上一页|更多)$', content_lower):
            return True

        return False

    def _clean_content(self, content: str) -> str:
        """
        清理和规范化内容。

        参数:
            content: 原始内容

        返回:
            清理后的内容
        """
        # 移除多余空白
        content = re.sub(r'\s+', ' ', content).strip()

        # 移除HTML标签（如果有）
        content = re.sub(r'<[^>]+>', '', content)

        # 移除可能是伪影的特殊字符
        content = re.sub(r'[^\w\s\u4e00-\u9fff.,!?;:()"\'-]', '', content)

        return content
    
    def _extract_thinking_processes(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        从消息中提取思考过程。

        参数:
            messages: 过滤后的消息

        返回:
            思考过程数据列表
        """
        if not self.config.get('include_thinking', True):
            return []

        thinking_processes = []

        for message in messages:
            content = message.get('content', '')

            # 检查多种思考内容的可能性
            thinking_content = self._extract_thinking_from_message(message)

            if thinking_content:
                for think_text in thinking_content:
                    if think_text and len(think_text.strip()) > 10:  # 过滤太短的内容
                        thinking_data = {
                            "content": self._clean_content(think_text),
                            "timestamp": message.get('timestamp'),
                            "confidence": self._calculate_thinking_confidence(think_text),
                            "keywords": self._extract_keywords(think_text),
                            "length": len(think_text),
                            "source": message.get('source', 'unknown'),
                            "type": "thinking"
                        }
                        thinking_processes.append(thinking_data)

            # 原有的检查逻辑作为后备
            elif message.get('type') == 'thinking' or self._is_thinking_content(content):
                thinking_data = {
                    "content": self._clean_content(content),
                    "timestamp": message.get('timestamp'),
                    "confidence": self._calculate_thinking_confidence(content),
                    "keywords": self._extract_keywords(content),
                    "length": len(content),
                    "source": message.get('source', 'unknown'),
                    "type": "thinking"
                }
                thinking_processes.append(thinking_data)

        return thinking_processes

    def _extract_thinking_from_message(self, message: Dict[str, Any]) -> List[str]:
        """从消息中提取思考内容"""
        thinking_content = []

        try:
            content = message.get('content', '')

            # 如果内容是字符串，尝试解析为 JSON
            if isinstance(content, str) and content.strip().startswith('{'):
                try:
                    # 清理转义字符
                    cleaned_content = self._clean_json_string(content)
                    parsed_content = json.loads(cleaned_content)

                    # 递归提取思考内容
                    thinking_content.extend(self._extract_thinking_from_nested_data(parsed_content))

                except json.JSONDecodeError:
                    pass

            # 如果内容已经是字典
            elif isinstance(content, dict):
                thinking_content.extend(self._extract_thinking_from_nested_data(content))

            # 检查是否包含思考关键词
            if isinstance(content, str):
                thinking_indicators = ['思考', 'thinking', '分析', 'analysis', '推理', 'reasoning', '考虑', 'consider']
                if any(indicator in content.lower() for indicator in thinking_indicators):
                    thinking_content.append(content)

        except Exception as e:
            self.logger.error(f"从消息提取思考时出错: {e}")

        return thinking_content

    def _extract_thinking_from_nested_data(self, data: dict) -> List[str]:
        """从嵌套数据中提取思考内容"""
        thinking_parts = []

        try:
            # 检查直接的思考字段
            thinking_fields = ['think', 'thinking', 'reasoning', 'analysis', 'thought', 'cbatchat']
            for field in thinking_fields:
                if field in data and data[field]:
                    thinking_text = str(data[field])
                    if thinking_text.strip():
                        thinking_parts.append(thinking_text)

            # 递归处理嵌套对象
            for key, value in data.items():
                if isinstance(value, dict):
                    nested_thinking = self._extract_thinking_from_nested_data(value)
                    thinking_parts.extend(nested_thinking)
                elif isinstance(value, list):
                    for item in value:
                        if isinstance(item, dict):
                            nested_thinking = self._extract_thinking_from_nested_data(item)
                            thinking_parts.extend(nested_thinking)

        except Exception as e:
            self.logger.error(f"从嵌套数据提取思考时出错: {e}")

        return thinking_parts

    def _clean_json_string(self, json_str: str) -> str:
        """清理 JSON 字符串中的多余转义字符"""
        try:
            if not json_str:
                return ""

            # 移除多余的转义斜杠
            cleaned = str(json_str)

            # 处理多层转义的情况 - 限制迭代次数防止无限循环
            iteration_count = 0
            max_iterations = 5
            while '\\\\' in cleaned and iteration_count < max_iterations:
                cleaned = cleaned.replace('\\\\', '\\')
                iteration_count += 1

            # 处理转义的引号
            cleaned = cleaned.replace('\\"', '"')

            # 处理其他常见转义字符 - 但保持JSON兼容性
            # 将换行符等转换为空格，避免JSON解析错误
            cleaned = cleaned.replace('\\n', ' ')
            cleaned = cleaned.replace('\\r', ' ')
            cleaned = cleaned.replace('\\t', ' ')

            return cleaned
        except Exception as e:
            self.logger.error(f"清理JSON字符串时出错: {e}")
            return str(json_str)
    
    def _extract_chat_results(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        从消息中提取聊天结果。

        参数:
            messages: 过滤后的消息

        返回:
            聊天结果数据列表
        """
        if not self.config.get('include_chat_results', True):
            return []

        chat_results = []

        for message in messages:
            if message.get('type') == 'response' or self._is_chat_result(message.get('content', '')):
                result_data = {
                    "content": message.get('content', ''),
                    "timestamp": message.get('timestamp'),
                    "length": len(message.get('content', '')),
                    "keywords": self._extract_keywords(message.get('content', ''))
                }
                chat_results.append(result_data)

        return chat_results

    def _is_thinking_content(self, content: str) -> bool:
        """
        检查内容是否为思考过程。

        参数:
            content: 要检查的内容

        返回:
            如果内容是思考过程则返回True
        """
        for pattern in self.thinking_patterns:
            if re.search(pattern, content):
                return True

        # 检查推理指示词
        reasoning_indicators = ['因为', '所以', '首先', '其次', '然后', '最后', '综上', '总结']
        return any(indicator in content for indicator in reasoning_indicators)

    def _is_chat_result(self, content: str) -> bool:
        """
        检查内容是否为聊天结果。

        参数:
            content: 要检查的内容

        返回:
            如果内容是聊天结果则返回True
        """
        # 聊天结果通常更长且更完整
        return len(content) > 50 and not self._is_thinking_content(content)
    
    def _calculate_thinking_confidence(self, content: str) -> float:
        """
        计算思考过程识别的置信度分数。

        参数:
            content: 要分析的内容

        返回:
            0到1之间的置信度分数
        """
        score = 0.0

        # 检查思考模式
        for pattern in self.thinking_patterns:
            if re.search(pattern, content):
                score += 0.3

        # 检查推理词汇
        reasoning_words = ['分析', '考虑', '评估', '判断', '推断', '假设', '验证']
        for word in reasoning_words:
            if word in content:
                score += 0.1

        return min(1.0, score)

    def _extract_keywords(self, content: str) -> List[str]:
        """
        从内容中提取关键词。

        参数:
            content: 要分析的内容

        返回:
            关键词列表
        """
        # 简单的关键词提取（可以用NLP增强）
        words = re.findall(r'\b\w{2,}\b', content)

        # 过滤常见停用词
        stop_words = {'的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'}
        keywords = [word for word in words if word not in stop_words and len(word) > 1]

        # 返回前10个最频繁的关键词
        from collections import Counter
        word_counts = Counter(keywords)
        return [word for word, count in word_counts.most_common(10)]
    
    def _build_conversation_flow(self, messages: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        构建对话流程结构。

        参数:
            messages: 过滤后的消息

        返回:
            对话流程数据
        """
        flow = []

        for i, message in enumerate(messages):
            flow_item = {
                "sequence": i + 1,
                "type": message.get('type', 'unknown'),
                "content_preview": message.get('content', '')[:100] + '...' if len(message.get('content', '')) > 100 else message.get('content', ''),
                "timestamp": message.get('timestamp'),
                "word_count": len(message.get('content', '').split())
            }
            flow.append(flow_item)

        return flow

    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """
        创建错误响应结构。

        参数:
            error_message: 错误消息

        返回:
            错误响应数据
        """
        return {
            "error": True,
            "message": error_message,
            "timestamp": datetime.now().isoformat(),
            "data": None
        }
