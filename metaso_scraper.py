"""
Metaso.cn 爬虫 - 用于自动化从 Metaso.cn 提取搜索结果数据的爬虫类

此模块提供主要的 MetasoScraper 类，作为从 Metaso.cn 网站
爬取搜索结果和 AI 响应数据的入口点。
"""

import asyncio
import json
from typing import Dict, Any, Optional
from pathlib import Path
import logging

from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page

try:
    from .core.config_manager import ConfigManager
    from .core.page_objects import MetasoPage
    from .core.human_behavior import HumanBehavior
    from .utils.logger import setup_logger
    from .utils.helpers import validate_parameters, retry_on_failure
except ImportError:
    # 处理直接运行时的导入问题
    from core.config_manager import ConfigManager
    from core.page_objects import MetasoPage
    from core.human_behavior import HumanBehavior
    from utils.logger import setup_logger
    from utils.helpers import validate_parameters, retry_on_failure


class MetasoScraper:
    """
    用于从 Metaso.cn 网站提取搜索结果数据的主爬虫类。

    此类提供自动化提取搜索结果和 AI 响应数据的公共接口。
    """

    def __init__(self, config_path: Optional[str] = None, proxy_config: Optional[Dict[str, str]] = None, login_config: Optional[Dict[str, str]] = None):
        """
        初始化 Metaso 爬虫。

        参数:
            config_path: 配置文件路径（可选）
            proxy_config: 代理配置字典（可选）
            login_config: 登录配置字典，包含 username 和 password（可选）
        """
        # 加载配置
        self.config_manager = ConfigManager(config_path)

        # 设置日志
        logging_config = self.config_manager.get_logging_config()
        self.logger = setup_logger(logging_config)

        # 浏览器相关属性
        self.playwright = None
        self.browser: Optional[Browser] = None
        self.context: Optional[BrowserContext] = None
        self.page: Optional[Page] = None
        self.metaso_page: Optional[MetasoPage] = None
        self.human_behavior: Optional[HumanBehavior] = None

        # 登录配置 - 优先使用传入的配置，否则从配置文件读取
        self.login_config = login_config or self.config_manager.get_metaso_login_config() or {}

        # 如果提供了代理配置则覆盖
        if proxy_config:
            self._update_proxy_config(proxy_config)

        self.logger.info("Metaso 爬虫初始化成功")

    async def scrape_metaso_content(self, search_query: str) -> Dict[str, Any]:
        """
        从 Metaso.cn 爬取搜索结果数据的主要公共方法。

        这是爬虫的主要入口点。它接受搜索查询参数，
        并返回包含提取的 steps 数据的结构化 JSON 数据。

        参数:
            search_query: 要搜索的查询字符串

        返回:
            包含结构化搜索结果数据的字典，JSON格式:
            {
                "success": bool,
                "data": {...},  # 提取的 steps 数据
                "metadata": {...},
                "session_id": str,
                "search_query": str
            }

        异常:
            ValueError: 如果参数无效
            Exception: 如果爬取失败
        """
        # 验证输入参数
        if not search_query or not search_query.strip():
            raise ValueError("search_query 必须是非空字符串")

        self.logger.info(f"开始 Metaso 搜索爬取 - 查询: {search_query}")

        try:
            # 初始化浏览器
            await self._initialize_browser()

            # 导航到 Metaso 主页
            await self.metaso_page.navigate_to_metaso()

            # 模拟人类行为
            await self.human_behavior.random_delay()
            await self.human_behavior.random_mouse_movement()

            # 输入搜索查询
            await self.metaso_page.input_search_query(search_query)

            # 提交搜索
            await self.metaso_page.submit_search()

            # 等待页面跳转完成并等待所有网络请求完成
            self.logger.info("等待页面跳转完成并等待所有网络请求完成...")
            await self.metaso_page.wait_for_navigation_and_network_idle()

            # 刷新页面并等待 API 响应，使用重试机制
            # 3次重试，每次等待时间从3秒开始，每次延长2秒（3s, 5s, 7s, 9s）
            self.logger.info("开始刷新页面并等待 API 响应...")
            result = await self.metaso_page.refresh_and_wait_for_api(max_retries=3, base_timeout=3000)
            self.logger.info(f"刷新页面并等待 API 响应完成，结果: {result}")

            if result["success"]:
                # 构建成功响应
                structured_data = {
                    "success": True,
                    "data": result["data"],
                    "metadata": {
                        "search_query": search_query,
                        "session_id": result["session_id"],
                        "timestamp": asyncio.get_event_loop().time(),
                        "source": "metaso_api_capture",
                        "api_responses_count": len(result.get("api_responses", [])),
                        "retry_info": {
                            "successful_attempt": result.get("attempt", 1),
                            "total_attempts": result.get("total_attempts", 1)
                        }
                    },
                    "session_id": result["session_id"],
                    "search_query": search_query
                }

                self.logger.info(f"Metaso 搜索爬取成功完成，第 {result.get('attempt', 1)} 次尝试成功")
                return structured_data
            else:
                # 构建失败响应
                error_response = {
                    "success": False,
                    "error": result.get("error", "未知错误"),
                    "data": None,
                    "metadata": {
                        "search_query": search_query,
                        "session_id": result.get("session_id"),
                        "timestamp": asyncio.get_event_loop().time(),
                        "source": "metaso_api_capture",
                        "api_responses_count": len(result.get("api_responses", [])),
                        "retry_info": {
                            "total_attempts": result.get("total_attempts", 1),
                            "all_failed": True
                        }
                    },
                    "session_id": result.get("session_id"),
                    "search_query": search_query
                }

                self.logger.error(f"Metaso 搜索爬取失败: {result.get('error')}，共尝试 {result.get('total_attempts', 1)} 次")
                return error_response

        except Exception as e:
            self.logger.error(f"爬取失败: {e}")
            return self._create_error_response(str(e), search_query)

        finally:
            await self._cleanup()

    def set_proxy(self, proxy_config: Dict[str, str]) -> None:
        """
        为爬虫设置代理配置。

        参数:
            proxy_config: 包含代理设置的字典:
                - server: 代理服务器URL（必需）
                - username: 代理用户名（可选）
                - password: 代理密码（可选）
        """
        self._update_proxy_config(proxy_config)
        self.logger.info("代理配置已更新")

    async def clear_saved_cookies(self) -> None:
        """清除保存的登录 cookies。"""
        try:
            if self.metaso_page:
                await self.metaso_page.clear_cookies()
            else:
                # 如果页面对象不存在，直接删除文件
                cookie_file = Path("metaso_cookies.json")
                if cookie_file.exists():
                    cookie_file.unlink()
                    self.logger.info("已清除保存的 cookies")
        except Exception as e:
            self.logger.error(f"清除 cookies 失败: {e}")

    async def _initialize_browser(self) -> None:
        """初始化 Playwright 浏览器和页面。"""
        try:
            self.playwright = await async_playwright().start()

            # 获取浏览器配置
            browser_config = self.config_manager.get_browser_config()
            proxy_config = self.config_manager.get_proxy_config()

            # 启动浏览器
            launch_options = {
                "headless": browser_config.get("headless", False),
                "timeout": browser_config.get("timeout", 300000),
                # "executable_path": "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"  # macOS 下 Chrome 的默认路径
            }

            self.browser = await self.playwright.chromium.launch(**launch_options)

            # 如果配置了代理则创建带代理的上下文
            context_options = {
                "viewport": browser_config.get("viewport", {"width": 1920, "height": 1080}),
                "user_agent": browser_config.get("user_agent")
            }

            if proxy_config:
                context_options["proxy"] = {
                    "server": proxy_config["server"],
                    "username": proxy_config.get("username"),
                    "password": proxy_config.get("password")
                }

            self.context = await self.browser.new_context(**context_options)
            self.page = await self.context.new_page()

            # 初始化页面对象
            self.metaso_page = MetasoPage(self.page, self.login_config)
            self.human_behavior = HumanBehavior(self.page, self.config_manager.get_human_behavior_config())

            self.logger.info("浏览器初始化成功")

        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {e}")
            await self._cleanup()
            raise

    def _update_proxy_config(self, proxy_config: Dict[str, str]) -> None:
        """
        更新代理配置。

        参数:
            proxy_config: 代理配置字典
        """
        if "server" not in proxy_config:
            raise ValueError("代理配置必须包含 'server'")

        self.config_manager.update_config("proxy.enabled", True)
        self.config_manager.update_config("proxy.server", proxy_config["server"])

        if "username" in proxy_config:
            self.config_manager.update_config("proxy.username", proxy_config["username"])

        if "password" in proxy_config:
            self.config_manager.update_config("proxy.password", proxy_config["password"])

    def _create_error_response(self, error_message: str, search_query: str = "") -> Dict[str, Any]:
        """
        创建标准化错误响应。

        参数:
            error_message: 错误消息
            search_query: 搜索查询（可选）

        返回:
            错误响应字典
        """
        return {
            "success": False,
            "error": error_message,
            "data": None,
            "metadata": {
                "search_query": search_query,
                "session_id": None,
                "timestamp": asyncio.get_event_loop().time(),
                "source": "metaso_scraper_error"
            },
            "session_id": None,
            "search_query": search_query
        }

    async def _cleanup(self) -> None:
        """清理浏览器资源。"""
        try:
            if self.page:
                await self.page.close()
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if self.playwright:
                await self.playwright.stop()

            self.logger.info("浏览器清理完成")

        except Exception as e:
            self.logger.error(f"清理过程中出错: {e}")

    async def __aenter__(self):
        """异步上下文管理器入口。"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出。"""
        await self._cleanup()


# 直接使用的便利函数
async def scrape_metaso_content(search_query: str,
                               config_path: Optional[str] = None,
                               proxy_config: Optional[Dict[str, str]] = None,
                               login_config: Optional[Dict[str, str]] = None) -> Dict[str, Any]:
    """
    爬取 Metaso.cn 搜索结果数据的便利函数。

    参数:
        search_query: 搜索查询字符串
        config_path: 配置文件路径（可选）
        proxy_config: 代理配置（可选）
        login_config: 登录配置，包含 username 和 password（可选）
                     如果不提供，将自动从配置文件的 metaso_login 部分读取

    返回:
        结构化的搜索结果数据
    """
    async with MetasoScraper(config_path, proxy_config, login_config) as scraper:
        return await scraper.scrape_metaso_content(search_query)
