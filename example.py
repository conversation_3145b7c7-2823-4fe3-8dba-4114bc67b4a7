"""
豆包爬虫使用示例

此文件演示如何使用 DoubaoScraper 提取对话数据，
包含流式API响应捕获功能。
"""

import asyncio
import json
import sys
from pathlib import Path

# 将父目录添加到路径以便正确导入
sys.path.insert(0, str(Path(__file__).parent.parent))

from py_scripts.doubao_scraper import DoubaoScraper, scrape_doubao_conversation


async def basic_example():
    """使用AI文章提示的基本示例。"""
    print("=== 基本使用示例（AI文章提示）===")

    try:
        # 创建爬虫实例
        scraper = DoubaoScraper()

        # 使用AI文章提示模板爬取对话数据
        result = await scraper.scrape_conversation(
            language="中文",
            topic="人工智能教育",
            proposition_direction="记叙文"
        )

        # 打印结果
        if not result.get('error'):
            print(f"爬取成功完成！")
            print(f"发现 {len(result.get('thinking_processes', []))} 个思考过程")
            print(f"和 {len(result.get('chat_results', []))} 个聊天结果。")

            # 保存到文件
            with open("ai_article_conversation.json", "w", encoding="utf-8") as f:
                json.dump(result, f, ensure_ascii=False, indent=2)

            print("结果已保存到 ai_article_conversation.json")
        else:
            print(f"爬取失败: {result.get('message')}")

    except Exception as e:
        print(f"错误: {e}")


async def proxy_example():
    """代理配置示例。"""
    print("\n=== 代理使用示例 ===")

    # 代理配置
    proxy_config = {
        "server": "http://proxy.example.com:8080",
        "username": "your_username",  # 可选
        "password": "your_password"   # 可选
    }

    try:
        # 使用代理创建爬虫
        scraper = DoubaoScraper(proxy_config=proxy_config)

        result = await scraper.scrape_conversation(
            language="English",
            topic="Climate Change Impact",
            proposition_direction="Support"
        )

        print(f"代理爬取完成。状态: {'成功' if not result.get('error') else '失败'}")

    except Exception as e:
        print(f"代理示例错误: {e}")


async def prompt_manager_example():
    """演示提示管理器功能的示例。"""
    print("\n=== 提示管理器示例 ===")

    try:
        from py_scripts.core.prompt_manager import PromptManager

        # 创建提示管理器
        prompt_manager = PromptManager()

        # 列出可用提示
        available_prompts = prompt_manager.list_available_prompts()
        print(f"可用提示模板: {available_prompts}")

        # 创建AI文章提示
        if "ai_article" in available_prompts:
            prompt = prompt_manager.get_ai_article_prompt(
                language="中文",
                topic="气候变化",
                proposition_direction="说明文"
            )
            print(f"生成的提示长度: {len(prompt)} 个字符")
            print(f"提示预览: {prompt[:200]}...")
        else:
            print("未找到 ai_article.txt 模板")

    except Exception as e:
        print(f"提示管理器错误: {e}")


async def convenience_function_example():
    """使用便利函数的示例。"""
    print("\n=== 便利函数示例 ===")

    try:
        # 使用便利函数
        result = await scrape_doubao_conversation(
            language="中文",
            topic="环境保护的重要性",
            proposition_direction="说明文"
        )

        if not result.get('error'):
            print("便利函数成功完成")

            # 打印摘要
            metadata = result.get('metadata', {})
            print(f"主题: {metadata.get('topic')}")
            print(f"语言: {metadata.get('language')}")
            print(f"总消息数: {metadata.get('total_messages')}")
        else:
            print(f"错误: {result.get('message')}")

    except Exception as e:
        print(f"便利函数错误: {e}")


async def context_manager_example():
    """使用异步上下文管理器的示例。"""
    print("\n=== 上下文管理器示例 ===")

    try:
        async with DoubaoScraper() as scraper:
            result = await scraper.scrape_conversation(
                language="中文",
                topic="科技发展对教育的影响",
                proposition_direction="支持"
            )

            if not result.get('error'):
                print("上下文管理器示例完成")

                # 分析思考过程
                thinking_processes = result.get('thinking_processes', [])
                if thinking_processes:
                    print(f"第一个思考过程: {thinking_processes[0]['content'][:100]}...")

                # 分析聊天结果
                chat_results = result.get('chat_results', [])
                if chat_results:
                    print(f"第一个聊天结果长度: {chat_results[0]['length']} 个字符")

        # 使用上下文管理器自动清理
        print("自动清理完成")

    except Exception as e:
        print(f"上下文管理器错误: {e}")


async def batch_scraping_example():
    """批量爬取多个主题的示例。"""
    print("\n=== 批量爬取示例 ===")

    topics = [
        ("中文", "人工智能教育", "记叙文"),
        ("中文", "环境保护", "说明文"),
        ("English", "Technology Innovation", "Support")
    ]

    results = []

    for language, topic, direction in topics:
        try:
            print(f"爬取中: {topic} ({language}, {direction})")

            result = await scrape_doubao_conversation(language, topic, direction)

            if not result.get('error'):
                results.append({
                    "topic": topic,
                    "language": language,
                    "direction": direction,
                    "thinking_count": len(result.get('thinking_processes', [])),
                    "chat_count": len(result.get('chat_results', []))
                })
                print(f"  ✓ 成功")
            else:
                print(f"  ✗ 失败: {result.get('message')}")

            # 在请求之间添加延迟
            await asyncio.sleep(5)

        except Exception as e:
            print(f"  ✗ 错误: {e}")

    # 打印摘要
    print(f"\n批量爬取完成。成功爬取了 {len(results)} 个主题:")
    for result in results:
        print(f"  - {result['topic']}: {result['thinking_count']} 个思考, {result['chat_count']} 个聊天")


async def main():
    """运行所有示例。"""
    print("豆包爬虫示例")
    print("=" * 50)

    # 运行示例
    await basic_example()
    await prompt_manager_example()
    # await proxy_example()  # 如果有代理请取消注释
    await convenience_function_example()
    await context_manager_example()
    await batch_scraping_example()

    print("\n" + "=" * 50)
    print("所有示例完成!")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
