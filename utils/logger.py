"""
豆包爬虫的日志工具
"""

import logging
import sys
from pathlib import Path
from typing import Dict, Any


def setup_logger(config: Dict[str, Any], name: str = "doubao_scraper") -> logging.Logger:
    """
    使用配置设置日志记录器。

    参数:
        config: 日志配置
        name: 日志记录器名称

    返回:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)

    # 清除现有处理器
    logger.handlers.clear()

    # 设置级别
    level = config.get('level', 'INFO').upper()
    logger.setLevel(getattr(logging, level, logging.INFO))

    # 创建格式化器
    format_string = config.get('format', '%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    formatter = logging.Formatter(format_string)

    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 如果指定则添加文件处理器
    log_file = config.get('file')
    if log_file and log_file.strip():  # 检查文件路径是否存在且不为空
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)

        file_handler = logging.FileHandler(log_path, encoding='utf-8')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        print(f"日志将保存到文件: {log_path}")
    else:
        print("未配置日志文件路径，日志仅输出到控制台")

    return logger
