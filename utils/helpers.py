"""
豆包爬虫的辅助工具
"""

import asyncio
import functools
from typing import Callable, Any, Optional
from playwright.async_api import Page, Locator
import logging


def retry_on_failure(max_retries: int = 3, delay: float = 1.0):
    """
    失败时重试函数的装饰器。

    参数:
        max_retries: 最大重试次数
        delay: 重试间隔（秒）
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs) -> Any:
            logger = logging.getLogger(__name__)
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries:
                        logger.error(f"Function {func.__name__} failed after {max_retries} retries: {e}")
                        raise
                    
                    logger.warning(f"函数 {func.__name__} 失败 (尝试 {attempt + 1}/{max_retries + 1}): {e}")
                    await asyncio.sleep(delay * (attempt + 1))  # 指数退避

        return wrapper
    return decorator


async def wait_for_element(page: Page, selector: str, timeout: int = 30000,
                          state: str = "visible") -> Optional[Locator]:
    """
    等待元素以指定状态出现。

    参数:
        page: Playwright页面实例
        selector: CSS选择器
        timeout: 超时时间（毫秒）
        state: 要等待的元素状态

    返回:
        如果找到则返回元素定位器，否则返回None
    """
    try:
        await page.wait_for_selector(selector, state=state, timeout=timeout)
        return page.locator(selector)
    except Exception as e:
        logging.getLogger(__name__).error(f"元素未找到: {selector} - {e}")
        return None


async def safe_click(element: Locator, timeout: int = 5000) -> bool:
    """
    安全地点击元素并处理错误。

    参数:
        element: 要点击的元素
        timeout: 超时时间（毫秒）

    返回:
        如果点击成功返回True，否则返回False
    """
    try:
        await element.wait_for(state="visible", timeout=timeout)
        await element.click(timeout=timeout)
        return True
    except Exception as e:
        logging.getLogger(__name__).error(f"点击元素失败: {e}")
        return False


async def safe_fill(element: Locator, text: str, timeout: int = 5000) -> bool:
    """
    安全地向元素填充文本。

    参数:
        element: 要填充的元素
        text: 要填充的文本
        timeout: 超时时间（毫秒）

    返回:
        如果填充成功返回True，否则返回False
    """
    try:
        await element.wait_for(state="visible", timeout=timeout)
        await element.clear()
        await element.fill(text)
        return True
    except Exception as e:
        logging.getLogger(__name__).error(f"填充元素失败: {e}")
        return False


async def get_element_text(element: Locator, timeout: int = 5000) -> Optional[str]:
    """
    安全地从元素获取文本。

    参数:
        element: 要获取文本的元素
        timeout: 超时时间（毫秒）

    返回:
        如果成功返回元素文本，否则返回None
    """
    try:
        await element.wait_for(state="visible", timeout=timeout)
        return await element.inner_text()
    except Exception as e:
        logging.getLogger(__name__).error(f"获取元素文本失败: {e}")
        return None


def validate_parameters(language: str, topic: str, proposition_direction: str) -> bool:
    """
    验证输入参数。

    参数:
        language: 语言参数
        topic: 主题参数
        proposition_direction: 命题方向参数

    返回:
        如果参数有效返回True，否则返回False
    """
    if not all([language, topic, proposition_direction]):
        return False

    if not all(isinstance(param, str) for param in [language, topic, proposition_direction]):
        return False

    if any(len(param.strip()) == 0 for param in [language, topic, proposition_direction]):
        return False

    return True
