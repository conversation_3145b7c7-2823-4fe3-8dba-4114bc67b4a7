# DC文件上传工具

这是一个用于批量上传题目到DC系统的Python工具。

## 功能特性

1. **批量处理**: 支持批量处理文件夹中的所有JSON文件
2. **智能来源选择**: 当指定的来源没有找到时，自动选择第一个可用选项
3. **详细日志**: 提供详细的处理过程日志，便于跟踪上传状态
4. **错误处理**: 完善的错误处理机制，单个文件失败不影响其他文件处理

## 使用方法

### 1. 批量处理文件夹

```bash
python dc_uploader.py <文件夹路径>
```

例如：
```bash
python dc_uploader.py /path/to/json/files
```

### 2. 单文件测试模式

```bash
python dc_uploader.py
```

不提供参数时，会使用默认的单文件测试模式。

## JSON文件格式

JSON文件应包含以下字段：

```json
{
  "编号": "题目编号",
  "学科": "数学",
  "题型": "单选题",
  "科目": "数二",
  "来源": ["线性代数-日练习", "行列式", "基础"],
  "年份": 2024,
  "题干": "题目内容",
  "选项": [
    {"choice": "A", "context": "选项A的内容"},
    {"choice": "B", "context": "选项B的内容"},
    {"choice": "C", "context": "选项C的内容"},
    {"choice": "D", "context": "选项D的内容"}
  ],
  "答案": "A",
  "分析点评": "分析点评内容",
  "文字解答": "文字解答内容"
}
```

## 重要说明

1. **认证令牌**: 使用前需要更新代码中的JWT令牌
2. **来源选择**: 如果指定的来源路径没有找到，系统会自动选择第一个可用选项
3. **浏览器要求**: 需要安装Chrome浏览器，工具会自动启动浏览器进行操作
4. **网络要求**: 需要能够访问DC系统的网络环境

## 处理结果

批量处理完成后，会显示处理统计：
- 成功上传的文件数量
- 失败的文件数量
- 总文件数量

每个文件的处理结果都会实时显示，便于跟踪进度。

## 错误处理

- JSON格式错误：会跳过该文件并继续处理其他文件
- 网络错误：会记录错误信息并继续处理
- 文件读取错误：会记录错误信息并继续处理

## 依赖要求

- Python 3.7+
- playwright
- requests
- pathlib (Python标准库)

安装依赖：
```bash
pip install playwright requests
playwright install chromium
```
