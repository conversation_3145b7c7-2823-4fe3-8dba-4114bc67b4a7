#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整的输入流程
"""

import json
from dc_uploader import DCUploader


def test_complete_flow():
    """测试完整的输入流程"""
    # 示例JWT令牌（请替换为实际的令牌）
    token = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTM0MzY3NzQsIm5iZiI6MTc1MzQzNjc3NCwiZXhwIjoxNzUzNTIzMTc0LCJkYXRhIjp7InVzZXJfaWQiOjIxLCJ1c2VyX25hbWUiOiJzaGl0aWx1cnUifX0.8yo8POhmXCIRqFSLIq3AF0yazAS66QM4aQgFQqknW-I"

    # 创建上传器实例
    uploader = DCUploader(token)

    # 创建测试数据
    test_data = {
        "编号": "测试-完整流程-001",
        "学科": "数学",
        "题型": "单选题",
        "科目": "数一",
        "题干": "下列关于矩阵特征值的说法正确的是（）",
        "选项": [
            {
                "choice": "A",
                "context": "矩阵的特征值一定是实数"
            },
            {
                "choice": "B", 
                "context": "矩阵的特征值可能是复数"
            }
        ],
        "分析点评": "本题考查矩阵特征值的基本性质。",
        "答案": "B"
    }

    print("开始测试完整的输入流程...")
    print("包括：点击源按钮 -> 清空内容 -> 输入内容 -> 再次点击源按钮")
    
    try:
        result = uploader.upload_quest(test_data)
        print(f"测试结果: {result}")
    except Exception as e:
        print(f"测试失败: {str(e)}")


if __name__ == "__main__":
    test_complete_flow()
