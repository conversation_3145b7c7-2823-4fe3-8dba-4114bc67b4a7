{"name": "codepage", "version": "1.15.0", "author": "SheetJS", "description": "pure-JS library to handle codepages", "keywords": ["codepage", "iconv", "convert", "strings"], "main": "cputils.js", "types": "types", "browser": {"buffer": "false"}, "devDependencies": {"voc": "~1.1.0", "mocha": "~2.5.3", "blanket": "~1.2.3", "@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "@types/commander": "^2.12.0", "dtslint": "^0.1.2", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/js-codepage.git"}, "scripts": {"pretest": "git submodule init && git submodule update", "test": "make test", "build": "make js", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "[cputils.js]"}}, "alex": {"allow": ["chinese", "european", "german", "japanese", "latin"]}, "homepage": "https://sheetjs.com/", "files": ["LICENSE", "README.md", "bin", "bits/*.js", "types/index.d.ts", "types/*.json", "cptable.js", "cputils.js", "dist/sbcs.full.js", "dist/cpexcel.full.js"], "bugs": {"url": "https://github.com/SheetJS/js-codepage/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}}