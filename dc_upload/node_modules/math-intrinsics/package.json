{"name": "math-intrinsics", "version": "1.1.0", "description": "ES Math-related intrinsics and helpers, robustly cached.", "main": false, "exports": {"./abs": "./abs.js", "./floor": "./floor.js", "./isFinite": "./isFinite.js", "./isInteger": "./isInteger.js", "./isNaN": "./isNaN.js", "./isNegativeZero": "./isNegativeZero.js", "./max": "./max.js", "./min": "./min.js", "./mod": "./mod.js", "./pow": "./pow.js", "./sign": "./sign.js", "./round": "./round.js", "./constants/maxArrayLength": "./constants/maxArrayLength.js", "./constants/maxSafeInteger": "./constants/maxSafeInteger.js", "./constants/maxValue": "./constants/maxValue.js", "./package.json": "./package.json"}, "sideEffects": false, "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "posttest": "npx npm@'>= 10.2' audit --production", "prelint": "evalmd README.md && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git' | grep -v dist/)", "lint": "eslint --ext=js,mjs .", "postlint": "tsc && attw -P", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "repository": {"type": "git", "url": "git+https://github.com/es-shims/math-intrinsics.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/es-shims/math-intrinsics/issues"}, "homepage": "https://github.com/es-shims/math-intrinsics#readme", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/for-each": "^0.3.3", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.8.0", "auto-changelog": "^2.5.0", "eclint": "^2.8.1", "es-value-fixtures": "^1.5.0", "eslint": "^8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}, "engines": {"node": ">= 0.4"}}