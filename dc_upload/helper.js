

const fs = require('fs');
const path = require('path');

// 需要先安装这些依赖: npm install form-data axios
const FormData = require('form-data');
const axios = require('axios');

const cleanDir = (dirPath) => {
    // 删除所有非 json 文件
    fs.readdir(dirPath, (err, files) => {
        if (err) throw err;

        files.forEach(file => {
            const filePath = path.join(dirPath, file);
            const extname = path.extname(file);

            if (extname !== '.json') {
                fs.unlink(filePath, (err) => {
                    if (err) throw err;
                    console.log(`Deleted ${filePath}`);
                });
            }
        });
    });
}


const setSource = (filename) => {
    const content = fs.readFileSync(filename, 'utf8');
    const json = JSON.parse(content);
    const name = json['编号'].replace('880题数二', '880题数学二');
    const group = name.split('-')[1];
    const sourceArr = group.split('_');
    sourceArr.pop();
    json['来源'] = sourceArr;
    fs.writeFileSync(filename, JSON.stringify(json, null, 2), 'utf8');
    return json;
}

const setSources = () => {
    const dirPath = '/Users/<USER>/Downloads/book_01/html_vis';
    fs.readdir(dirPath, (err, files) => {
        if (err) throw err;

        files.forEach(file => {
            if (file.endsWith('.json')) {
                const filePath = path.join(dirPath, file);
                setSource(filePath);
            }
        });
    });
}


// 需要先安装 xlsx: npm install xlsx
const XLSX = require('xlsx');

const setName = () => {
    const excelPath = '/Users/<USER>/Downloads/编号转换结果.xlsx';
    const dirPath = '/Users/<USER>/Downloads/book_01/html_vis';
    const workbook = XLSX.readFile(excelPath);
    const sheetName = workbook.SheetNames[0];
    const sheet = workbook.Sheets[sheetName];
    const rows = XLSX.utils.sheet_to_json(sheet, { header: 1 });

    // 跳过表头
    for (let i = 1; i < rows.length; i++) {
        const row = rows[i];
        const filename = row[0]; // 第一列
        const newId = row[2];    // 第三列
        if (!filename || !newId) continue;
        const filePath = path.join(dirPath, filename);
        if (!fs.existsSync(filePath)) {
            console.log(`文件不存在: ${filePath}`);
            continue;
        }
        try {
            console.log(`正在处理文件: ${filePath}`);
            const content = fs.readFileSync(filePath, 'utf8');

            const json = JSON.parse(content);
            json['编号'] = newId;
            fs.writeFileSync(filePath, JSON.stringify(json, null, 2), 'utf8');
            console.log(`已更新编号: ${filePath}`);
        } catch (err) {
            console.error(`处理失败: ${filePath}`, err);
        }
    }
}

// 打印所有 json 文件的 '题型' 字段
const printQuestionTypes = () => {
    const dirPath = '/Users/<USER>/Downloads/book_01/html_vis';
    fs.readdir(dirPath, (err, files) => {
        if (err) throw err;
        files.forEach(file => {
            if (file.endsWith('.json')) {
                const filePath = path.join(dirPath, file);
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    const json = JSON.parse(content);
                    console.log(`${json['题型']}`);
                } catch (err) {
                    console.error(`读取失败: ${filePath}`, err);
                }
            }
        });
    });
}


// 处理所有单选题，提取并修改答案字段为 A-D
const processSingleChoice = () => {
    const dirPath = '/Users/<USER>/Downloads/book_01/html_vis';
    fs.readdir(dirPath, (err, files) => {
        if (err) throw err;
        files.forEach(file => {
            if (file.endsWith('.json')) {
                const filePath = path.join(dirPath, file);
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    const json = JSON.parse(content);
                    if (json['题型'] === '选择题' && json['答案']) {
                        // 答案可能是 "A. xxx" 或 "A"
                        let ans = json['答案'];
                        let match = ans.match(/[A-D]/);
                        if (match) {
                            json['答案'] = match[0];
                            fs.writeFileSync(filePath, JSON.stringify(json, null, 2), 'utf8');
                            console.log(`${file}: 答案已处理为 ${match[0]}`);
                        } else {
                            console.log(`${file}: 未找到 A-D 答案`);
                        }
                    }
                } catch (err) {
                    console.error(`处理失败: ${filePath}`, err);
                }
            }
        });
    });
}

// 处理所有选择题题型，修改为单选题
const processQuestionType = () => {
    const dirPath = '/Users/<USER>/Downloads/book_01/html_vis';
    fs.readdir(dirPath, (err, files) => {
        if (err) throw err;
        files.forEach(file => {
            if (file.endsWith('.json')) {
                const filePath = path.join(dirPath, file);
                try {
                    const content = fs.readFileSync(filePath, 'utf8');
                    const json = JSON.parse(content);
                    if (json['题型'] === '计算题') {
                        json['题型'] = '解答题'
                        fs.writeFileSync(filePath, JSON.stringify(json, null, 2), 'utf8');
                    }
                } catch (err) {
                    console.error(`处理失败: ${filePath}`, err);
                }
            }
        });
    });
}

// 处理图片链接，上传并替换为img标签
const processImageLinks = async () => {
    const dirPath = '/Users/<USER>/Downloads/book_01/html_vis';
    const imagePath = '/Users/<USER>/Downloads/book_01/images/book_01';
    
    // 你的JWT认证令牌，请替换为实际的令牌
    const authToken = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTM2ODg0OTksIm5iZiI6MTc1MzY4ODQ5OSwiZXhwIjoxNzUzNzc0ODk5LCJkYXRhIjp7InVzZXJfaWQiOjIxLCJ1c2VyX25hbWUiOiJzaGl0aWx1cnUifX0.eqNfsxs9iN0khPdFsvptWkbOjCzuJ1WitozdDy6f6yY";
    
    // 实际的上传函数，基于dc_uploader.py的逻辑
    const uploadImage = async (filePath) => {
        try {
            // 检查文件是否存在
            if (!fs.existsSync(filePath)) {
                throw new Error(`文件不存在: ${filePath}`);
            }
            
            const fileName = path.basename(filePath);
            const form = new FormData();
            
            // 添加文件到表单
            form.append('file', fs.createReadStream(filePath), fileName);
            
            // 请求头
            const headers = {
                ...form.getHeaders(),
                "authorization": authToken,
                "client": "admin_login_scene:",
                "accept": "application/json, text/plain, */*",
                "origin": "https://demo.dc.zentaotech.com",
                "referer": "https://demo.dc.zentaotech.com/"
            };
            
            // 发送上传请求
            const response = await axios.post(
                'https://api.dc.zentaotech.com/adminapi/file/upload',
                form,
                {
                    headers: headers,
                    timeout: 30000
                }
            );
            
            // 检查响应
            if (response.data && response.data.code === 200 && response.data.data) {
                const fileUrl = response.data.data.fileUrl;
                console.log(`上传成功: ${fileName} -> ${fileUrl}`);
                return fileUrl;
            } else {
                throw new Error(`上传失败: ${JSON.stringify(response.data)}`);
            }
            
        } catch (error) {
            console.error(`上传图片失败 ${filePath}:`, error.message);
            return null;
        }
    };
    
    const processValue = async (value) => {
        if (typeof value !== 'string') return value;
        
        // 匹配图片链接模式 - 支持任意文件名的png图片
        const imageRegex = /!\[[^[\]]*\.png\]\(http:\/\/ysj-question\.oss-cn-hangzhou\.aliyuncs\.com\/Yjs\/\d+\/([a-f0-9-]+)\.png_yjs\)/g;
        
        let newValue = value;
        const matches = [...value.matchAll(imageRegex)];
        
        for (const match of matches) {
            const fullMatch = match[0];
            const fileId = match[1]; // 提取文件名后缀
            const localImagePath = path.join(imagePath, `${fileId}.png`);
            
            if (fs.existsSync(localImagePath)) {
                try {
                    const imgUrl = await uploadImage(localImagePath);
                    if (imgUrl) {
                        const imgTag = `<img src="${imgUrl}" />`;
                        newValue = newValue.replace(fullMatch, imgTag);
                        console.log(`已替换图片: ${fileId} -> ${imgUrl}`);
                    } else {
                        console.log(`上传失败，保留原链接: ${fileId}`);
                    }
                } catch (err) {
                    console.error(`上传失败: ${fileId}`, err);
                }
            } else {
                console.log(`图片文件不存在: ${localImagePath}`);
            }
        }
        
        return newValue;
    };
    
    const processObject = async (obj) => {
        if (Array.isArray(obj)) {
            const newArray = [];
            for (const item of obj) {
                newArray.push(await processObject(item));
            }
            return newArray;
        } else if (typeof obj === 'object' && obj !== null) {
            const newObj = {};
            for (const [key, value] of Object.entries(obj)) {
                newObj[key] = await processObject(value);
            }
            return newObj;
        } else {
            return await processValue(obj);
        }
    };
    
    const files = fs.readdirSync(dirPath).filter(file => file.endsWith('.json'));
    
    for (const file of files) {
        const filePath = path.join(dirPath, file);
        try {
            console.log(`处理文件: ${file}`);
            const content = fs.readFileSync(filePath, 'utf8');
            const json = JSON.parse(content);
            
            const updatedJson = await processObject(json);
            
            fs.writeFileSync(filePath, JSON.stringify(updatedJson, null, 2), 'utf8');
            console.log(`已更新文件: ${file}`);
        } catch (err) {
            console.error(`处理失败: ${file}`, err);
        }
    }
};

const main = async () => {
    // cleanDir('/Users/<USER>/Downloads/book_01/html_vis');
    // setSources();
    // setName();

// cleanDir('/Users/<USER>/Downloads/book_01/html_vis')

// setSource('/Users/<USER>/Downloads/book_01/html_vis/李-880题数二_高等数学 第二章 一元函数微分学及其应用_10-1_57693.json');

    processQuestionType();
    await processImageLinks(); // 启用图片处理
}

main().catch(console.error)